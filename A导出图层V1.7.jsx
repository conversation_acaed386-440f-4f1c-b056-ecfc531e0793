// 全局变量
var exportOptions = {
    source: "current", // current, all, folder
    format: "jpg", // jpg, png, tif, psd, pdf
    exportGroups: false,
    hideHiddenLayers: false,
    cropTransparency: false,
    folderPath: "",
    exportPath: "", // 导出路径
    // JPG设置
    jpgQuality: 10,
    jpgMatte: 0, // 0=白色, 1=黑色, 2=灰色, 3=无, 4=背景色, 5=前景色
    jpgIcc: true,
    jpgOptimized: false,
    jpgProgressive: false,
    // PNG设置
    pngBitDepth: 24,
    pngTransparency: true,
    pngInterlaced: false,
    pngMatte: 0, // 0=白色, 1=黑色, 2=灰色, 3=无, 4=背景色, 5=前景色
    // TIF设置
    tifCompression: "TIFFEncoding.NONE", // 改为字符串存储
    tifEncoding: 0, // 压缩类型索引
    tifBitDepth: "BitsPerChannelType.EIGHT", // 改为字符串存储
    tifTransparency: true,
    tifAlphaChannel: false, // Alpha通道
    tifIcc: true,
    tifQuality: 100, // JPEG压缩时的品质
    // PSD设置
    psdLayers: false, // 是否保留图层
    // PDF设置
    pdfStandard: 0, // PDF标准
    pdfCompatibility: 1, // PDF兼容性
    pdfQuality: 100, // JPEG压缩品质
    pdfEncoding: 2, // 编码方式: 0=无, 1=ZIP, 2=JPEG
    pdfAlphaChannel: false,
    pdfIcc: true,
    pdfColorConversion: false,
    pdfDestinationProfile: 5,
    pdfDownSample: 3,
    pdfDownSampleSize: 300,
    pdfDownSampleSizeLimit: 450,
    // 前景/背景设置
    exportForeground: false, // 顶层作为固定前景图
    exportBackground: false  // 底层作为固定背景图
};

// 配置文件路径
var configPath = Folder.userData + "/Adobe/Logs/A恒心Photoshop图层导出选项.json";

// 辅助函数：创建简单的选项更新事件处理
function createOptionHandler(optionName) {
    return function() {
        exportOptions[optionName] = this.value;
    };
}

// 加载配置文件
function loadConfig() {
    try {
        var configFile = new File(configPath);
        if (configFile.exists) {
            configFile.open("r");
            var configData = configFile.read();
            configFile.close();
          
            var savedOptions = eval("(" + configData + ")");
            for (var key in savedOptions) {
                if (exportOptions.hasOwnProperty(key)) {
                    exportOptions[key] = savedOptions[key];
                }
            }
            
            // 转换字符串回对象引用
            if (typeof exportOptions.tifCompression === "string") {
                switch (exportOptions.tifCompression) {
                    case "TIFFEncoding.NONE": exportOptions.tifCompression = TIFFEncoding.NONE; break;
                    case "TIFFEncoding.TIFFLZW": exportOptions.tifCompression = TIFFEncoding.TIFFLZW; break;
                    case "TIFFEncoding.TIFFZIP": exportOptions.tifCompression = TIFFEncoding.TIFFZIP; break;
                    case "TIFFEncoding.JPEG": exportOptions.tifCompression = TIFFEncoding.JPEG; break;
                    default: exportOptions.tifCompression = TIFFEncoding.NONE;
                }
            }
            
            if (typeof exportOptions.tifBitDepth === "string") {
                switch (exportOptions.tifBitDepth) {
                    case "BitsPerChannelType.EIGHT": exportOptions.tifBitDepth = BitsPerChannelType.EIGHT; break;
                    case "BitsPerChannelType.SIXTEEN": exportOptions.tifBitDepth = BitsPerChannelType.SIXTEEN; break;
                    default: exportOptions.tifBitDepth = BitsPerChannelType.EIGHT;
                }
            }
        }
    } catch (e) {
        // 配置文件加载失败，使用默认设置
        // alert("配置加载失败: " + e);
    }
}

// 保存配置文件
function saveConfig() {
    try {
        var configFile = new File(configPath);
        var configDir = new Folder(configFile.parent);
        if (!configDir.exists) {
            configDir.create();
        }
      
        // 将对象引用转换为字符串存储
        var tifCompressionStr = "TIFFEncoding.NONE";
        switch (exportOptions.tifCompression.toString()) {
            case TIFFEncoding.NONE.toString(): tifCompressionStr = "TIFFEncoding.NONE"; break;
            case TIFFEncoding.TIFFLZW.toString(): tifCompressionStr = "TIFFEncoding.TIFFLZW"; break;
            case TIFFEncoding.TIFFZIP.toString(): tifCompressionStr = "TIFFEncoding.TIFFZIP"; break;
            case TIFFEncoding.JPEG.toString(): tifCompressionStr = "TIFFEncoding.JPEG"; break;
        }
        
        var tifBitDepthStr = "BitsPerChannelType.EIGHT";
        switch (exportOptions.tifBitDepth.toString()) {
            case BitsPerChannelType.EIGHT.toString(): tifBitDepthStr = "BitsPerChannelType.EIGHT"; break;
            case BitsPerChannelType.SIXTEEN.toString(): tifBitDepthStr = "BitsPerChannelType.SIXTEEN"; break;
        }
      
        // 手动序列化配置对象
        var configStr = "{\n";
        configStr += '    "source": "' + exportOptions.source + '",\n';
        configStr += '    "format": "' + exportOptions.format + '",\n';
        configStr += '    "exportGroups": ' + exportOptions.exportGroups + ',\n';
        configStr += '    "hideHiddenLayers": ' + exportOptions.hideHiddenLayers + ',\n';
        configStr += '    "cropTransparency": ' + exportOptions.cropTransparency + ',\n';
        configStr += '    "folderPath": "' + exportOptions.folderPath.replace(/\\/g, "\\\\").replace(/"/g, '\\"') + '",\n';
        configStr += '    "exportPath": "' + exportOptions.exportPath.replace(/\\/g, "\\\\").replace(/"/g, '\\"') + '",\n';
        // JPG设置
        configStr += '    "jpgQuality": ' + exportOptions.jpgQuality + ',\n';
        configStr += '    "jpgMatte": ' + exportOptions.jpgMatte + ',\n';
        configStr += '    "jpgIcc": ' + exportOptions.jpgIcc + ',\n';
        configStr += '    "jpgOptimized": ' + exportOptions.jpgOptimized + ',\n';
        configStr += '    "jpgProgressive": ' + exportOptions.jpgProgressive + ',\n';
        // PNG设置
        configStr += '    "pngBitDepth": ' + exportOptions.pngBitDepth + ',\n';
        configStr += '    "pngTransparency": ' + exportOptions.pngTransparency + ',\n';
        configStr += '    "pngInterlaced": ' + exportOptions.pngInterlaced + ',\n';
        configStr += '    "pngMatte": ' + exportOptions.pngMatte + ',\n';
        // TIF设置
        configStr += '    "tifCompression": "' + tifCompressionStr + '",\n';
        configStr += '    "tifEncoding": ' + exportOptions.tifEncoding + ',\n';
        configStr += '    "tifBitDepth": "' + tifBitDepthStr + '",\n';
        configStr += '    "tifTransparency": ' + exportOptions.tifTransparency + ',\n';
        configStr += '    "tifAlphaChannel": ' + exportOptions.tifAlphaChannel + ',\n';
        configStr += '    "tifIcc": ' + exportOptions.tifIcc + ',\n';
        configStr += '    "tifQuality": ' + exportOptions.tifQuality + ',\n';
        // PSD设置
        configStr += '    "psdLayers": ' + exportOptions.psdLayers + ',\n';
        // PDF设置
        configStr += '    "pdfStandard": ' + exportOptions.pdfStandard + ',\n';
        configStr += '    "pdfCompatibility": ' + exportOptions.pdfCompatibility + ',\n';
        configStr += '    "pdfQuality": ' + exportOptions.pdfQuality + ',\n';
        configStr += '    "pdfEncoding": ' + exportOptions.pdfEncoding + ',\n';
        configStr += '    "pdfAlphaChannel": ' + exportOptions.pdfAlphaChannel + ',\n';
        configStr += '    "pdfIcc": ' + exportOptions.pdfIcc + ',\n';
        configStr += '    "pdfColorConversion": ' + exportOptions.pdfColorConversion + ',\n';
        configStr += '    "pdfDestinationProfile": ' + exportOptions.pdfDestinationProfile + ',\n';
        configStr += '    "pdfDownSample": ' + exportOptions.pdfDownSample + ',\n';
        configStr += '    "pdfDownSampleSize": ' + exportOptions.pdfDownSampleSize + ',\n';
        configStr += '    "pdfDownSampleSizeLimit": ' + exportOptions.pdfDownSampleSizeLimit + ',\n';
        // 前景/背景设置
        configStr += '    "exportForeground": ' + exportOptions.exportForeground + ',\n';
        configStr += '    "exportBackground": ' + exportOptions.exportBackground + '\n';
        configStr += "}";
      
        configFile.open("w");
        configFile.write(configStr);
        configFile.close();
    } catch (e) {
        // 配置文件保存失败，显示错误信息
        alert("配置保存失败: " + e);
    }
}

// 主函数
function main() {
    try {
        // 检查是否有文档打开
        if (app.documents.length === 0) {
            alert("请先打开一个文档！");
            return;
        }

        // 加载配置
        loadConfig();

        // 创建用户界面
        var dialog = createDialog();
      
        // 显示对话框
        if (dialog.show() === 1) {
            // 保存配置
            saveConfig();
            // 用户点击确定，开始导出
            processExport();
        }
    } catch (e) {
        alert("发生错误: " + e);
    }
}

// 创建对话框
function createDialog() {
    var dialog = new Window("dialog", "A导出图层V1.7");
    dialog.orientation = "column";
    dialog.alignChildren = ["fill", "top"];
    dialog.spacing = 10;
    dialog.margins = 16;

    // ---- 作者信息 - 顶部 ----
    var authorGroup = dialog.add("group");
    authorGroup.orientation = "row";
    authorGroup.alignChildren = ["center", "center"];
    authorGroup.alignment = ["center", "top"];

    var authorText = authorGroup.add("statictext", undefined, "恒心-perseverance");
    authorText.graphics.foregroundColor = authorText.graphics.newPen(authorText.graphics.PenType.SOLID_COLOR, [1, 0.5, 0], 1);

    // ---- 源文档选择区域 ----
    var sourcePanel = dialog.add("panel", undefined, "处理范围");
    sourcePanel.orientation = "column";
    sourcePanel.alignChildren = ["left", "top"];
    sourcePanel.margins = [10, 15, 10, 10];
  
    // 单选按钮组 - 横向排列
    var sourceGroup = sourcePanel.add("group");
    sourceGroup.orientation = "row";
    sourceGroup.alignChildren = ["left", "center"];
    sourceGroup.spacing = 20;

    var currentDocRadio = sourceGroup.add("radiobutton", undefined, "当前文档");
    currentDocRadio.value = true;
    currentDocRadio.helpTip = "仅处理当前激活的文档";

    var allDocsRadio = sourceGroup.add("radiobutton", undefined, "所有打开的文档");
    allDocsRadio.helpTip = "处理所有已打开的文档";

    var folderDocsRadio = sourceGroup.add("radiobutton", undefined, "指定文件夹内的文档");
    folderDocsRadio.helpTip = "处理指定文件夹内的所有PSD/PSB文件";
  
    // 文件夹选择组
    var folderGroup = sourcePanel.add("group");
    folderGroup.orientation = "row";
    folderGroup.alignChildren = ["left", "center"];
    folderGroup.enabled = false;
  
    var folderPathText = folderGroup.add("edittext", undefined, "");
    folderPathText.preferredSize.width = 250;
    folderPathText.enabled = true;
  
    var browseButton = folderGroup.add("button", undefined, "浏览...");
    browseButton.preferredSize.width = 80;
  
    // 源文档选择事件处理 - 修复配置保存问题
    currentDocRadio.onClick = function() {
        if (this.value) {
            exportOptions.source = "current";
            folderGroup.enabled = false;
        }
    };
    
    allDocsRadio.onClick = function() {
        if (this.value) {
            exportOptions.source = "all";
            folderGroup.enabled = false;
        }
    };
    
    folderDocsRadio.onClick = function() {
        if (this.value) {
            exportOptions.source = "folder";
            folderGroup.enabled = true;
        }
    };
  
    browseButton.onClick = function() {
        var sourceFolder = Folder.selectDialog("选择包含PSD/PSB文件的文件夹");
        if (sourceFolder) {
            folderPathText.text = sourceFolder.fsName;
            exportOptions.folderPath = sourceFolder.fsName;
        }
    };

    // 文件夹路径输入框事件
    folderPathText.onChanging = function() {
        exportOptions.folderPath = this.text;
    };

    // ---- 导出路径选择区域 ----
    var exportPathPanel = dialog.add("panel", undefined, "导出路径");
    exportPathPanel.orientation = "column";
    exportPathPanel.alignChildren = ["left", "top"];
    exportPathPanel.margins = [10, 15, 10, 10];
  
    var exportPathGroup = exportPathPanel.add("group");
    exportPathGroup.orientation = "row";
    exportPathGroup.alignChildren = ["left", "center"];
  
    var exportPathText = exportPathGroup.add("edittext", undefined, exportOptions.exportPath || "");
    exportPathText.preferredSize.width = 250;
    exportPathText.enabled = true;
    exportPathText.helpTip = "选择图层导出的保存路径";
  
    var exportBrowseButton = exportPathGroup.add("button", undefined, "浏览...");
    exportBrowseButton.preferredSize.width = 80;
  
    exportBrowseButton.onClick = function() {
        var targetFolder = Folder.selectDialog("选择图层导出保存路径");
        if (targetFolder) {
            exportPathText.text = targetFolder.fsName;
            exportOptions.exportPath = targetFolder.fsName;
        }
    };

    // 导出路径输入框事件
    exportPathText.onChanging = function() {
        exportOptions.exportPath = this.text;
    };
    // ---- 导出格式选择区域 ----
    var formatPanel = dialog.add("panel", undefined, "导出格式");
    formatPanel.orientation = "column";
    formatPanel.alignChildren = ["left", "top"];
    formatPanel.margins = [10, 15, 10, 10];
  
    // 格式选择按钮组 - 改为按钮样式
    var formatGroup = formatPanel.add("group");
    formatGroup.orientation = "row";
    formatGroup.alignChildren = ["left", "center"];
    formatGroup.spacing = 2;

    var jpgButton = formatGroup.add("button", undefined, "JPG");
    jpgButton.preferredSize.width = 50;
    jpgButton.preferredSize.height = 25;

    var png8Button = formatGroup.add("button", undefined, "PNG");
    png8Button.preferredSize.width = 50;
    png8Button.preferredSize.height = 25;

    var tifButton = formatGroup.add("button", undefined, "TIFF");
    tifButton.preferredSize.width = 50;
    tifButton.preferredSize.height = 25;

    var pdfButton = formatGroup.add("button", undefined, "PDF");
    pdfButton.preferredSize.width = 50;
    pdfButton.preferredSize.height = 25;

    var psdButton = formatGroup.add("button", undefined, "PSD");
    psdButton.preferredSize.width = 50;
    psdButton.preferredSize.height = 25;

    // 当前选中的格式按钮
    var currentFormatButton = jpgButton; // 默认选中JPG
    currentFormatButton.text = "●" + currentFormatButton.text;

    // 格式按钮切换函数
    function selectFormatButton(button, format) {
        // 重置所有按钮
        currentFormatButton.text = currentFormatButton.text.replace("●", "");
        // 设置新选中的按钮
        currentFormatButton = button;
        button.text = "●" + button.text.replace("●", "");
        exportOptions.format = format;

        // 显示对应的选项面板
        png8Options.visible = (format === "png8");
        jpgOptions.visible = (format === "jpg");
        tifOptions.visible = (format === "tif");
        pdfOptions.visible = (format === "pdf");
        psdOptions.visible = (format === "psd");
    }

    // 按钮点击事件
    jpgButton.onClick = function() { selectFormatButton(this, "jpg"); };
    png8Button.onClick = function() { selectFormatButton(this, "png8"); };
    tifButton.onClick = function() { selectFormatButton(this, "tif"); };
    pdfButton.onClick = function() { selectFormatButton(this, "pdf"); };
    psdButton.onClick = function() { selectFormatButton(this, "psd"); };
  
    // 格式选项面板（堆栈面板）
    var formatOptionsPanel = formatPanel.add("group");
    formatOptionsPanel.orientation = "stack";
    formatOptionsPanel.alignment = ["fill", "top"];



    // PNG-8选项
    var png8Options = formatOptionsPanel.add("group");
    png8Options.orientation = "column";
    png8Options.alignChildren = ["left", "top"];
    png8Options.visible = false;

    // PNG-8颜色和杂边设置
    var png8Group1 = png8Options.add("group");
    png8Group1.orientation = "row";
    png8Group1.alignChildren = ["left", "center"];
    png8Group1.spacing = 10;
    png8Group1.add("statictext", undefined, "颜色:");
    var png8ColorsText = png8Group1.add("edittext", undefined, "256");
    png8ColorsText.preferredSize.width = 50;
    png8Group1.add("statictext", undefined, "杂边:");
    var png8MatteDropdown = png8Group1.add("dropdownlist", undefined, ["白色", "黑色", "灰色", "无", "背景色", "前景色"]);
    png8MatteDropdown.selection = 0;
    png8MatteDropdown.preferredSize.width = 80;

    // PNG-8其他选项
    var png8TransparencyCheck = png8Options.add("checkbox", undefined, "透明背景");
    png8TransparencyCheck.value = true;
    var png8InterlacedCheck = png8Options.add("checkbox", undefined, "交错");
    png8InterlacedCheck.value = false;

    // PNG事件处理
    png8TransparencyCheck.onClick = createOptionHandler("pngTransparency");
    png8InterlacedCheck.onClick = createOptionHandler("pngInterlaced");

    png8MatteDropdown.onChange = function() {
        exportOptions.pngMatte = this.selection.index;
    };

    // JPG选项
    var jpgOptions = formatOptionsPanel.add("group");
    jpgOptions.orientation = "column";
    jpgOptions.alignChildren = ["left", "top"];
    jpgOptions.visible = true;

    // JPG品质设置
    var jpgGroup1 = jpgOptions.add("group");
    jpgGroup1.orientation = "row";
    jpgGroup1.alignChildren = ["left", "center"];
    jpgGroup1.spacing = 10;
    jpgGroup1.add("statictext", undefined, "品质:");
    var jpgQualitySlider = jpgGroup1.add("slider", undefined, 10, 0, 12);
    jpgQualitySlider.preferredSize.width = 200;
    var jpgQualityValue = jpgGroup1.add("statictext", undefined, "10");
    jpgQualityValue.preferredSize.width = 30;

    // JPG杂边设置
    var jpgGroup2 = jpgOptions.add("group");
    jpgGroup2.orientation = "row";
    jpgGroup2.alignChildren = ["left", "center"];
    jpgGroup2.spacing = 10;
    jpgGroup2.add("statictext", undefined, "设置背景色为:");
    var jpgMatteDropdown = jpgGroup2.add("dropdownlist", undefined, ["白色", "黑色", "灰色", "-", "背景色", "前景色"]);
    jpgMatteDropdown.selection = 0;
    jpgMatteDropdown.preferredSize.width = 80;

    jpgQualitySlider.onChanging = function() {
        jpgQualityValue.text = Math.round(this.value);
        exportOptions.jpgQuality = Math.round(this.value);
    };

    // JPG其他选项
    var jpgIccCheck = jpgOptions.add("checkbox", undefined, "嵌入 ICC 配置文件");
    jpgIccCheck.value = false;
    var jpgOptimizedCheck = jpgOptions.add("checkbox", undefined, "优化");
    jpgOptimizedCheck.value = false;
    var jpgProgressiveCheck = jpgOptions.add("checkbox", undefined, "连续");
    jpgProgressiveCheck.value = false;

    jpgMatteDropdown.onChange = function() {
        exportOptions.jpgMatte = this.selection.index;
    };

    jpgIccCheck.onClick = createOptionHandler("jpgIcc");
    jpgOptimizedCheck.onClick = createOptionHandler("jpgOptimized");
    jpgProgressiveCheck.onClick = createOptionHandler("jpgProgressive");
  

  
    // TIF选项
    var tifOptions = formatOptionsPanel.add("group");
    tifOptions.orientation = "column";
    tifOptions.alignChildren = ["left", "top"];
    tifOptions.visible = false;

    // TIF压缩设置
    var tifGroup1 = tifOptions.add("group");
    tifGroup1.orientation = "row";
    tifGroup1.alignChildren = ["left", "center"];
    tifGroup1.spacing = 10;
    tifGroup1.add("statictext", undefined, "图像压缩:");
    var tifCompressionDropdown = tifGroup1.add("dropdownlist", undefined, ["None", "LZW", "ZIP", "JPG"]);
    tifCompressionDropdown.selection = 0;
    tifCompressionDropdown.preferredSize.width = 80;

    // TIF品质设置（仅JPEG压缩时可用）
    var tifGroup2 = tifGroup1.add("group");
    tifGroup2.orientation = "row";
    tifGroup2.alignChildren = ["left", "center"];
    tifGroup2.spacing = 10;
    tifGroup2.add("statictext", undefined, "品质:");
    var tifQualitySlider = tifGroup2.add("slider", undefined, 100, 0, 100);
    tifQualitySlider.preferredSize.width = 100;
    var tifQualityValue = tifGroup2.add("statictext", undefined, "100");
    tifQualityValue.preferredSize.width = 25;
    tifGroup2.enabled = false; // 默认禁用，只有JPEG压缩时启用

    // TIF其他选项
    var tifTransparencyCheck = tifOptions.add("checkbox", undefined, "Alpha 通道");
    tifTransparencyCheck.value = false;
    var tifIccCheck = tifOptions.add("checkbox", undefined, "嵌入 ICC 配置文件");
    tifIccCheck.value = false;
    var tifTransparencyBgCheck = tifOptions.add("checkbox", undefined, "透明背景");
    tifTransparencyBgCheck.value = false;

    tifQualitySlider.onChanging = function() {
        tifQualityValue.text = Math.round(this.value);
        exportOptions.tifQuality = Math.round(this.value);
    };

    tifIccCheck.onClick = function() {
        exportOptions.tifIcc = this.value;
    };

    tifTransparencyCheck.onClick = function() {
        exportOptions.tifAlphaChannel = this.value;
    };

    tifTransparencyBgCheck.onClick = function() {
        exportOptions.tifTransparency = this.value;
    };

    tifCompressionDropdown.onChange = function() {
        tifGroup2.enabled = (this.selection.index === 3); // JPEG压缩时启用品质设置
        exportOptions.tifEncoding = this.selection.index;
    };



    // PSD选项
    var psdOptions = formatOptionsPanel.add("group");
    psdOptions.orientation = "column";
    psdOptions.alignChildren = ["left", "top"];
    psdOptions.visible = false;

    var psdLayersCheck = psdOptions.add("checkbox", undefined, "保留图层");
    psdLayersCheck.value = true;

    psdLayersCheck.onClick = function() {
        exportOptions.psdLayers = this.value;
    };

    // PDF选项
    var pdfOptions = formatOptionsPanel.add("group");
    pdfOptions.orientation = "column";
    pdfOptions.alignChildren = ["left", "top"];
    pdfOptions.visible = false;

    var pdfGroup1 = pdfOptions.add("group");
    pdfGroup1.orientation = "row";
    pdfGroup1.alignChildren = ["left", "center"];
    pdfGroup1.spacing = 5;
    pdfGroup1.add("statictext", undefined, "标准:");
    var pdfStandardDropdown = pdfGroup1.add("dropdownlist", undefined, ["无", "PDF/X-1a:2001", "PDF/X-1a:2003", "PDF/X-3:2002", "PDF/X-3:2003", "PDF/X-4:2008"]);
    pdfStandardDropdown.selection = 0;
    pdfStandardDropdown.preferredSize.width = 120;

    var pdfGroup2 = pdfOptions.add("group");
    pdfGroup2.orientation = "row";
    pdfGroup2.alignChildren = ["left", "center"];
    pdfGroup2.spacing = 5;
    pdfGroup2.add("statictext", undefined, "兼容:");
    var pdfCompatibilityDropdown = pdfGroup2.add("dropdownlist", undefined, ["Acrobat 4", "Acrobat 5", "Acrobat 6", "Acrobat 7", "Acrobat 8"]);
    pdfCompatibilityDropdown.selection = 1;
    pdfCompatibilityDropdown.preferredSize.width = 80;

    // 颜色转换 + 目标（同一行）
    var pdfGroup3 = pdfOptions.add("group");
    pdfGroup3.orientation = "row";
    pdfGroup3.alignChildren = ["left", "center"];
    pdfGroup3.spacing = 10;
    var pdfColorConversionCheck = pdfGroup3.add("checkbox", undefined, "颜色转换");
    pdfColorConversionCheck.value = false;

    var pdfDestinationGroup = pdfGroup3.add("group");
    pdfDestinationGroup.orientation = "row";
    pdfDestinationGroup.alignChildren = ["left", "center"];
    pdfDestinationGroup.spacing = 10;
    pdfDestinationGroup.add("statictext", undefined, "目标:");
    var pdfDestinationProfileDropdown = pdfDestinationGroup.add("dropdownlist", undefined, [
        "Japan Color 2001 Coated", "Japan Color 2001 Uncoated", "Japan Color 2002 Newspaper",
        "Japan Color 2003 Web Coated", "Japan Web Coated (Ad)", "U.S. Sheetfed Coated v2",
        "U.S. Sheetfed Uncoated v2", "U.S. Web Coated (SWOP) v2", "U.S. Web Uncoated v2",
        "-", "sRGB IEC61966-2.1", "Adobe RGB (1998)", "Apple RGB", "ColorMatch RGB",
        "ProPhoto RGB", "Rec.601 NTSC", "Rec.601 PAL", "Rec.709"
    ]);
    pdfDestinationProfileDropdown.selection = 10; // sRGB
    pdfDestinationProfileDropdown.preferredSize.width = 120;
    pdfDestinationProfileDropdown.enabled = false;

    // 缩减采样（包含所有PPI设置在同一行）
    var pdfGroup4 = pdfOptions.add("group");
    pdfGroup4.orientation = "row";
    pdfGroup4.alignChildren = ["left", "center"];
    pdfGroup4.spacing = 10;
    var pdfDownSampleDropdown = pdfGroup4.add("dropdownlist", undefined, ["不缩减像素采样", "平均缩减像素采样至", "次像素采样至", "双立方缩减像素采样至"]);
    pdfDownSampleDropdown.selection = 3;
    pdfDownSampleDropdown.preferredSize.width = 150;

    var pdfDownSampleSizeGroup = pdfGroup4.add("group");
    pdfDownSampleSizeGroup.orientation = "row";
    pdfDownSampleSizeGroup.alignChildren = ["left", "center"];
    pdfDownSampleSizeGroup.spacing = 5;
    var pdfDownSampleSizeText = pdfDownSampleSizeGroup.add("edittext", undefined, "300");
    pdfDownSampleSizeText.preferredSize.width = 40;
    pdfDownSampleSizeGroup.add("statictext", undefined, "分辨率若高于");
    var pdfDownSampleSizeLimitText = pdfDownSampleSizeGroup.add("edittext", undefined, "450");
    pdfDownSampleSizeLimitText.preferredSize.width = 40;
    pdfDownSampleSizeGroup.add("statictext", undefined, "PPI");

    // 压缩 + 质量（同一行）
    var pdfGroup5 = pdfOptions.add("group");
    pdfGroup5.orientation = "row";
    pdfGroup5.alignChildren = ["left", "center"];
    pdfGroup5.spacing = 10;
    pdfGroup5.add("statictext", undefined, "压缩:");
    var pdfEncodingDropdown = pdfGroup5.add("dropdownlist", undefined, ["无", "ZIP", "JPEG"]);
    pdfEncodingDropdown.selection = 2;
    pdfEncodingDropdown.preferredSize.width = 80;

    var pdfQualityGroup = pdfGroup5.add("group");
    pdfQualityGroup.orientation = "row";
    pdfQualityGroup.alignChildren = ["left", "center"];
    pdfQualityGroup.spacing = 10;
    pdfQualityGroup.add("statictext", undefined, "质量:");
    var pdfQualitySlider = pdfQualityGroup.add("slider", undefined, 100, 0, 100);
    pdfQualitySlider.preferredSize.width = 100;
    var pdfQualityValue = pdfQualityGroup.add("statictext", undefined, "100");
    pdfQualityValue.preferredSize.width = 25;

    // PDF其他选项
    var pdfAlphaChannelCheck = pdfOptions.add("checkbox", undefined, "Alpha 通道");
    pdfAlphaChannelCheck.value = false;
    var pdfIccCheck = pdfOptions.add("checkbox", undefined, "嵌入 ICC 配置文件");
    pdfIccCheck.value = false;

    // PDF事件处理
    pdfStandardDropdown.onChange = function() {
        exportOptions.pdfStandard = this.selection.index;
    };

    pdfCompatibilityDropdown.onChange = function() {
        exportOptions.pdfCompatibility = this.selection.index;
    };

    pdfEncodingDropdown.onChange = function() {
        pdfQualityGroup.enabled = (this.selection.index === 2); // JPEG编码时启用品质设置
        exportOptions.pdfEncoding = this.selection.index;
    };

    pdfQualitySlider.onChanging = function() {
        pdfQualityValue.text = Math.round(this.value);
        exportOptions.pdfQuality = Math.round(this.value);
    };

    pdfColorConversionCheck.onClick = function() {
        pdfDestinationProfileDropdown.enabled = this.value;
        exportOptions.pdfColorConversion = this.value;
    };

    pdfDestinationProfileDropdown.onChange = function() {
        exportOptions.pdfDestinationProfile = this.selection.index;
    };

    pdfDownSampleDropdown.onChange = function() {
        var enabled = this.selection.index > 0;
        pdfDownSampleSizeText.enabled = enabled;
        pdfDownSampleSizeLimitText.enabled = enabled;
        exportOptions.pdfDownSample = this.selection.index;
    };

    pdfDownSampleSizeText.onChange = function() {
        var size = parseInt(this.text, 10);
        if (isNaN(size)) size = 300;
        this.text = size;
        pdfDownSampleSizeLimitText.text = Math.round(size * 1.5);
        exportOptions.pdfDownSampleSize = size;
        exportOptions.pdfDownSampleSizeLimit = Math.round(size * 1.5);
    };

    pdfAlphaChannelCheck.onClick = function() {
        exportOptions.pdfAlphaChannel = this.value;
    };

    pdfIccCheck.onClick = function() {
        exportOptions.pdfIcc = this.value;
    };







    // ---- 图层筛选选项 ----
    var layerFilterPanel = dialog.add("panel", undefined, "图层筛选");
    layerFilterPanel.orientation = "column";
    layerFilterPanel.alignChildren = ["left", "top"];
    layerFilterPanel.margins = [10, 15, 10, 10];

    // 横向排列筛选选项
    var filterGroup = layerFilterPanel.add("group");
    filterGroup.orientation = "row";
    filterGroup.alignChildren = ["left", "center"];
    filterGroup.spacing = 15;

    var exportGroupsCheck = filterGroup.add("checkbox", undefined, "按图层组导出");
    exportGroupsCheck.helpTip = "选中后，将每个最外层组导出为一个文件";

    var hideHiddenLayersCheck = filterGroup.add("checkbox", undefined, "不导出隐藏图层");
    hideHiddenLayersCheck.helpTip = "选中后，将忽略所有隐藏的图层";

    var cropTransparencyCheck = filterGroup.add("checkbox", undefined, "导出前裁剪透明像素");
    cropTransparencyCheck.helpTip = "选中后，将在导出前裁剪掉图层周围的透明区域";

    exportGroupsCheck.onClick = function() {
        exportOptions.exportGroups = this.value;
    };

    hideHiddenLayersCheck.onClick = function() {
        exportOptions.hideHiddenLayers = this.value;
    };

    cropTransparencyCheck.onClick = function() {
        exportOptions.cropTransparency = this.value;
    };

    // ---- 前景/背景设置 ----
    var foregroundBackgroundPanel = dialog.add("panel", undefined, "前景/背景设置");
    foregroundBackgroundPanel.orientation = "column";
    foregroundBackgroundPanel.alignChildren = ["left", "top"];
    foregroundBackgroundPanel.margins = [10, 15, 10, 10];

    // 横向排列前景/背景选项
    var fgBgGroup = foregroundBackgroundPanel.add("group");
    fgBgGroup.orientation = "row";
    fgBgGroup.alignChildren = ["left", "center"];
    fgBgGroup.spacing = 15;

    var exportForegroundCheck = fgBgGroup.add("checkbox", undefined, "顶层作为固定前景图");
    exportForegroundCheck.helpTip = "选中后，将顶层作为固定前景图叠加到每个导出的图层上";

    var exportBackgroundCheck = fgBgGroup.add("checkbox", undefined, "底层作为固定背景图");
    exportBackgroundCheck.helpTip = "选中后，将底层作为固定背景图放置在每个导出的图层下";

    exportForegroundCheck.onClick = function() {
        exportOptions.exportForeground = this.value;
    };

    exportBackgroundCheck.onClick = function() {
        exportOptions.exportBackground = this.value;
    };

    // ---- 按钮区域 ----
    var buttonGroup = dialog.add("group");
    buttonGroup.orientation = "row";
    buttonGroup.alignChildren = ["center", "center"];
    buttonGroup.alignment = ["center", "top"];
    buttonGroup.spacing = 30; // 增加按钮间距

    var cancelButton = buttonGroup.add("button", undefined, "取消", {name: "cancel"});
    cancelButton.preferredSize.width = 80;
    cancelButton.preferredSize.height = 35;

    var okButton = buttonGroup.add("button", undefined, "确定", {name: "ok"});
    okButton.preferredSize.width = 80;
    okButton.preferredSize.height = 35;

    // 根据配置设置界面状态
    function updateUIFromConfig() {
        // 设置源文档选项
        switch (exportOptions.source) {
            case "current":
                currentDocRadio.value = true;
                folderGroup.enabled = false;
                break;
            case "all":
                allDocsRadio.value = true;
                folderGroup.enabled = false;
                break;
            case "folder":
                folderDocsRadio.value = true;
                folderGroup.enabled = true;
                break;
        }

        // 设置文件夹路径
        folderPathText.text = exportOptions.folderPath || "";

        // 设置导出路径
        exportPathText.text = exportOptions.exportPath || "";

        // 设置导出格式
        var formatMap = {
            "png8": png8Button,
            "jpg": jpgButton,
            "tif": tifButton,
            "pdf": pdfButton,
            "psd": psdButton
        };

        // 兼容旧配置中的png格式
        var currentFormat = exportOptions.format;
        if (currentFormat === "png" || currentFormat === "png24") {
            currentFormat = exportOptions.pngBitDepth === 8 ? "png8" : "jpg";
        }

        var targetButton = formatMap[currentFormat] || jpgButton;
        selectFormatButton(targetButton, currentFormat);

        // 设置JPG选项
        jpgQualitySlider.value = exportOptions.jpgQuality;
        jpgQualityValue.text = exportOptions.jpgQuality.toString();
        jpgMatteDropdown.selection = exportOptions.jpgMatte;
        jpgIccCheck.value = exportOptions.jpgIcc;
        jpgOptimizedCheck.value = exportOptions.jpgOptimized;
        jpgProgressiveCheck.value = exportOptions.jpgProgressive;

        // 设置PNG选项
        png8TransparencyCheck.value = exportOptions.pngTransparency;
        png8InterlacedCheck.value = exportOptions.pngInterlaced;
        png8MatteDropdown.selection = exportOptions.pngMatte;

        // 设置TIF选项
        var compressionIndex = 0;
        switch (exportOptions.tifCompression.toString()) {
            case TIFFEncoding.NONE.toString(): compressionIndex = 0; break;
            case TIFFEncoding.TIFFLZW.toString(): compressionIndex = 1; break;
            case TIFFEncoding.TIFFZIP.toString(): compressionIndex = 2; break;
            case TIFFEncoding.JPEG.toString(): compressionIndex = 3; break;
        }
        tifCompressionDropdown.selection = compressionIndex;


        tifTransparencyCheck.value = exportOptions.tifAlphaChannel || false;
        tifTransparencyBgCheck.value = exportOptions.tifTransparency;
        tifIccCheck.value = exportOptions.tifIcc;
        tifQualitySlider.value = exportOptions.tifQuality;
        tifQualityValue.text = exportOptions.tifQuality.toString();

        // 设置PSD选项
        psdLayersCheck.value = exportOptions.psdLayers;

        // 设置PDF选项
        pdfStandardDropdown.selection = exportOptions.pdfStandard;
        pdfCompatibilityDropdown.selection = exportOptions.pdfCompatibility;
        pdfEncodingDropdown.selection = exportOptions.pdfEncoding;
        pdfQualitySlider.value = exportOptions.pdfQuality;
        pdfQualityValue.text = exportOptions.pdfQuality.toString();
        pdfQualityGroup.enabled = (exportOptions.pdfEncoding === 2);
        pdfColorConversionCheck.value = exportOptions.pdfColorConversion;
        pdfDestinationProfileDropdown.selection = exportOptions.pdfDestinationProfile;
        pdfDestinationProfileDropdown.enabled = exportOptions.pdfColorConversion;
        pdfDownSampleDropdown.selection = exportOptions.pdfDownSample;
        pdfDownSampleSizeText.text = exportOptions.pdfDownSampleSize.toString();
        pdfDownSampleSizeLimitText.text = exportOptions.pdfDownSampleSizeLimit.toString();
        var downsampleEnabled = exportOptions.pdfDownSample > 0;
        pdfDownSampleSizeText.enabled = downsampleEnabled;
        pdfDownSampleSizeLimitText.enabled = downsampleEnabled;
        pdfAlphaChannelCheck.value = exportOptions.pdfAlphaChannel;
        pdfIccCheck.value = exportOptions.pdfIcc;

        // 设置图层筛选选项
        exportGroupsCheck.value = exportOptions.exportGroups;
        hideHiddenLayersCheck.value = exportOptions.hideHiddenLayers;
        cropTransparencyCheck.value = exportOptions.cropTransparency;

        // 设置前景/背景选项
        exportForegroundCheck.value = exportOptions.exportForeground;
        exportBackgroundCheck.value = exportOptions.exportBackground;
    }

    // 初始化界面
    updateUIFromConfig();

    // 移除原来的dialog.onClose，因为所有设置已经在各自的事件中实时更新
    
    return dialog;
}


// 创建进度条窗口
function createProgressWindow() {
    var progressWin = new Window("window", "导出进度");
    progressWin.orientation = "column";
    progressWin.alignChildren = ["fill", "top"];
    progressWin.spacing = 10;
    progressWin.margins = 16;
    progressWin.preferredSize.width = 400;

    var progressText = progressWin.add("statictext", undefined, "准备导出...");
    progressText.alignment = ["fill", "top"];

    var progressBar = progressWin.add("progressbar", undefined, 0, 100);
    progressBar.preferredSize.width = 350;
    progressBar.preferredSize.height = 20;

    var detailText = progressWin.add("statictext", undefined, "");
    detailText.alignment = ["fill", "top"];

    progressWin.show();

    return {
        window: progressWin,
        text: progressText,
        bar: progressBar,
        detail: detailText
    };
}

// 处理导出
function processExport() {
    var progressWindow = null;
    try {
        var docs = [];

        // 根据用户选择获取要处理的文档
        switch (exportOptions.source) {
            case "current":
                docs.push(app.activeDocument);
                break;
            case "all":
                for (var i = 0; i < app.documents.length; i++) {
                    docs.push(app.documents[i]);
                }
                break;
            case "folder":
                if (exportOptions.folderPath === "") {
                    alert("请选择一个文件夹！");
                    return;
                }

                var sourceFolder = new Folder(exportOptions.folderPath);
                var files = sourceFolder.getFiles(/(\.psd|\.psb)$/i);

                if (files.length === 0) {
                    alert("所选文件夹中没有找到PSD/PSB文件！");
                    return;
                }

                // 提示用户将打开多个文件
                var proceed = confirm("将打开 " + files.length + " 个文件进行处理。是否继续？");
                if (!proceed) return;

                // 打开文件夹中的所有PSD/PSB文件
                for (var j = 0; j < files.length; j++) {
                    try {
                        var docRef = app.open(files[j]);
                        docs.push(docRef);
                    } catch (e) {
                        alert("无法打开文件 " + files[j].name + ": " + e);
                    }
                }
                break;
        }

        // 检查导出路径
        if (exportOptions.exportPath === "") {
            alert("请选择导出路径！");
            return;
        }

        // 计算总图层数
        var totalLayers = 0;
        for (var i = 0; i < docs.length; i++) {
            var doc = docs[i];
            if (exportOptions.exportGroups) {
                // 只计算图层组数量
                totalLayers += doc.layerSets.length;
            } else {
                // 只计算单个图层数量（排除图层组和前景/背景图层）
                for (var j = 0; j < doc.layers.length; j++) {
                    if (doc.layers[j].typename !== "LayerSet") {
                        // 跳过被设置为前景图的顶层
                        if (exportOptions.exportForeground && j === 0) continue;

                        // 跳过被设置为背景图的底层
                        if (exportOptions.exportBackground && j === doc.layers.length - 1) continue;

                        totalLayers++;
                    }
                }
            }
        }

        // 创建进度条窗口
        try {
            progressWindow = createProgressWindow();
            if (progressWindow && progressWindow.text) {
                progressWindow.text.text = "准备导出 " + totalLayers + " 个图层...";
            }
        } catch (e) {
            // 如果进度窗口创建失败，继续执行但不显示进度
            progressWindow = null;
        }

        var processedLayers = 0;

        // 处理每个文档
        for (var k = 0; k < docs.length; k++) {
            var currentDoc = docs[k];
            app.activeDocument = currentDoc;

            if (progressWindow && progressWindow.detail) {
                progressWindow.detail.text = "当前文档: " + currentDoc.name + " (" + (k + 1) + "/" + docs.length + ")";
            }

            // 创建保存文件夹
            var docName = currentDoc.name.replace(/\.[^\.]+$/, "");
            var saveFolder = new Folder(exportOptions.exportPath + "/" + docName + "_导出");
            if (!saveFolder.exists) {
                saveFolder.create();
            }

            // 导出图层并更新进度
            processedLayers = exportLayers(currentDoc, saveFolder, progressWindow, processedLayers, totalLayers);

            // 如果是从文件夹打开的，处理完后关闭
            if (exportOptions.source === "folder") {
                currentDoc.close(SaveOptions.DONOTSAVECHANGES);
            }
        }

        // 完成进度
        if (progressWindow && progressWindow.bar && progressWindow.text && progressWindow.detail) {
            progressWindow.bar.value = 100;
            progressWindow.text.text = "导出完成！";
            progressWindow.detail.text = "共处理了 " + docs.length + " 个文档。";
        }

        // 延迟关闭进度窗口
        if (progressWindow && progressWindow.window) {
            progressWindow.window.close();
        }

        alert("导出完成！共处理了 " + docs.length + " 个文档。");
    } catch (e) {
        // 确保进度窗口关闭
        if (progressWindow && progressWindow.window) {
            progressWindow.window.close();
        }
        alert("导出过程中发生错误: " + e);
    }
}

// 导出图层函数
function exportLayers(doc, saveFolder, progressWindow, processedLayers, totalLayers) {
    // 保存当前可见性状态
    var layerVisibility = [];
    for (var i = 0; i < doc.layers.length; i++) {
        layerVisibility.push(doc.layers[i].visible);
    }

    try {
        // 如果选择按图层组导出
        if (exportOptions.exportGroups) {
            for (var j = 0; j < doc.layerSets.length; j++) {
                var layerSet = doc.layerSets[j];

                // 跳过隐藏的图层组（如果选择了不导出隐藏图层）
                if (exportOptions.hideHiddenLayers && !layerSet.visible) continue;

                // 更新进度
                processedLayers++;
                var progress = Math.round((processedLayers / totalLayers) * 100);
                if (progressWindow && progressWindow.bar && progressWindow.text) {
                    progressWindow.bar.value = progress;
                    progressWindow.text.text = "正在导出图层组: " + layerSet.name + " (" + processedLayers + "/" + totalLayers + ")";
                }

                // 隐藏所有图层
                hideAllLayers(doc);

                // 显示当前图层组
                layerSet.visible = true;

                // 导出图层组
                exportLayer(doc, layerSet, saveFolder);
            }
        } else {
            // 导出所有图层（只导出单个图层，跳过图层组）
            for (var k = 0; k < doc.layers.length; k++) {
                var layer = doc.layers[k];

                // 跳过图层组（LayerSet），只处理单个图层
                if (layer.typename === "LayerSet") continue;

                // 跳过隐藏的图层（如果选择了不导出隐藏图层）
                if (exportOptions.hideHiddenLayers && !layer.visible) continue;

                // 跳过被设置为前景图的顶层
                if (exportOptions.exportForeground && k === 0) continue;

                // 跳过被设置为背景图的底层
                if (exportOptions.exportBackground && k === doc.layers.length - 1) continue;

                // 处理背景图层 - 直接转换为普通图层
                if (layer.isBackgroundLayer) {
                    try {
                        // 直接将背景图层转换为普通图层
                        layer.isBackgroundLayer = false;
                    } catch (e) {
                        // 如果转换失败，跳过背景图层
                        continue;
                    }
                }

                // 更新进度
                processedLayers++;
                var progress = Math.round((processedLayers / totalLayers) * 100);
                if (progressWindow && progressWindow.bar && progressWindow.text) {
                    progressWindow.bar.value = progress;
                    progressWindow.text.text = "正在导出图层: " + layer.name + " (" + processedLayers + "/" + totalLayers + ")";
                }

                // 隐藏所有图层
                hideAllLayers(doc);

                // 显示当前图层
                layer.visible = true;

                // 导出图层
                exportLayer(doc, layer, saveFolder);
            }
        }
    } catch (e) {
        alert("导出图层时发生错误: " + e);
    } finally {
        // 恢复原始可见性状态
        for (var l = 0; l < doc.layers.length; l++) {
            if (l < layerVisibility.length) {
                doc.layers[l].visible = layerVisibility[l];
            }
        }
    }

    return processedLayers;
}

// 隐藏文档中的所有图层
function hideAllLayers(doc) {
    for (var i = 0; i < doc.layers.length; i++) {
        doc.layers[i].visible = false;
    }
}

// 导出单个图层
function exportLayer(doc, layer, saveFolder) {
    try {
        // 复制文档
        var docCopy = doc.duplicate();
        app.activeDocument = docCopy;

        // 处理前景/背景图层
        if (exportOptions.exportForeground || exportOptions.exportBackground) {
            // 隐藏所有图层
            for (var i = 0; i < docCopy.layers.length; i++) {
                docCopy.layers[i].visible = false;
            }

            // 显示背景图层（如果启用）
            if (exportOptions.exportBackground && docCopy.layers.length > 0) {
                var backgroundLayer = docCopy.layers[docCopy.layers.length - 1]; // 底层
                backgroundLayer.visible = true;
            }

            // 显示当前图层
            for (var j = 0; j < docCopy.layers.length; j++) {
                if (docCopy.layers[j].name === layer.name) {
                    docCopy.layers[j].visible = true;
                    break;
                }
            }

            // 显示前景图层（如果启用）
            if (exportOptions.exportForeground && docCopy.layers.length > 0) {
                var foregroundLayer = docCopy.layers[0]; // 顶层
                foregroundLayer.visible = true;
            }
        }

        // 拼合图像，避免图层转换问题
        try {
            // 检查是否有多个图层
            if (docCopy.layers.length > 1) {
                docCopy.flatten();
            }
        } catch (e) {
            // 如果拼合失败，继续处理
        }

        // 如果需要裁剪透明像素
        if (exportOptions.cropTransparency) {
            cropTransparentPixels(docCopy);
        }

        // 构建保存路径，清理文件名中的非法字符
        var cleanLayerName = layer.name;
        cleanLayerName = cleanLayerName.replace(/\\/g, "_");
        cleanLayerName = cleanLayerName.replace(/\//g, "_");
        cleanLayerName = cleanLayerName.replace(/:/g, "_");
        cleanLayerName = cleanLayerName.replace(/\*/g, "_");
        cleanLayerName = cleanLayerName.replace(/\?/g, "_");
        cleanLayerName = cleanLayerName.replace(/"/g, "_");
        cleanLayerName = cleanLayerName.replace(/</g, "_");
        cleanLayerName = cleanLayerName.replace(/>/g, "_");
        cleanLayerName = cleanLayerName.replace(/\|/g, "_");
        var savePath = new File(saveFolder + "/" + cleanLayerName);

        // 根据选择的格式导出
        switch (exportOptions.format) {
            case "jpg":
                saveAsJPG(docCopy, savePath);
                break;
            case "png8":
                exportOptions.pngBitDepth = 8;
                saveAsPNG(docCopy, savePath);
                break;
            case "png": // 兼容旧配置
            case "png24": // 兼容旧配置，转为PNG8
                exportOptions.pngBitDepth = 8;
                saveAsPNG(docCopy, savePath);
                break;
            case "tif":
                saveAsTIF(docCopy, savePath);
                break;
            case "psd":
                saveAsPSD(docCopy, savePath);
                break;
            case "pdf":
                saveAsPDF(docCopy, savePath);
                break;
        }

        // 关闭副本
        docCopy.close(SaveOptions.DONOTSAVECHANGES);
    } catch (e) {
        alert("导出图层 '" + layer.name + "' 时发生错误: " + e);
    }
}

// 裁剪透明像素
function cropTransparentPixels(doc) {
    try {
        app.activeDocument = doc;

        // 如果文档只有背景图层，先将其转换为普通图层
        if (doc.layers.length === 1 && doc.layers[0].isBackgroundLayer) {
            doc.layers[0].isBackgroundLayer = false;
        }

        // 使用修剪功能来裁剪透明像素
        // 修剪类型：透明像素，修剪方向：上下左右
        doc.trim(TrimType.TRANSPARENT, true, true, true, true);

    } catch (e) {
        // 如果修剪失败，可能是因为没有透明像素或其他原因，忽略错误继续处理
        // alert("裁剪透明像素时发生错误: " + e);
    }
}

// 保存为JPG
function saveAsJPG(doc, file) {
    var jpgFile = new File(file.toString() + ".jpg");
    var jpgSaveOptions = new JPEGSaveOptions();
    jpgSaveOptions.quality = exportOptions.jpgQuality;
    jpgSaveOptions.embedColorProfile = exportOptions.jpgIcc;

    // 设置杂边
    var matteTypes = [MatteType.WHITE, MatteType.BLACK, MatteType.SEMIGRAY, MatteType.NONE, MatteType.BACKGROUND, MatteType.FOREGROUND];
    jpgSaveOptions.matte = matteTypes[exportOptions.jpgMatte];

    // 设置格式选项
    if (exportOptions.jpgProgressive) {
        jpgSaveOptions.formatOptions = FormatOptions.PROGRESSIVE;
        jpgSaveOptions.scans = 3;
    } else if (exportOptions.jpgOptimized) {
        jpgSaveOptions.formatOptions = FormatOptions.OPTIMIZEDBASELINE;
    } else {
        jpgSaveOptions.formatOptions = FormatOptions.STANDARDBASELINE;
    }

    doc.saveAs(jpgFile, jpgSaveOptions, true, Extension.LOWERCASE);
}

// 保存为PNG
function saveAsPNG(doc, file) {
    var pngFile = new File(file.toString() + ".png");

    if (exportOptions.pngBitDepth === 8) {
        // 使用Save for Web导出PNG-8
        var exportOptions_web = new ExportOptionsSaveForWeb();
        exportOptions_web.format = SaveDocumentType.PNG;
        exportOptions_web.PNG8 = true;
        exportOptions_web.transparency = exportOptions.pngTransparency;
        exportOptions_web.interlaced = exportOptions.pngInterlaced;
        exportOptions_web.colors = 256;
        exportOptions_web.dither = Dither.NONE;
        doc.exportDocument(pngFile, ExportType.SAVEFORWEB, exportOptions_web);
    } else {
        // 使用常规PNG保存选项导出PNG-24
        var pngSaveOptions = new PNGSaveOptions();
        pngSaveOptions.interlaced = exportOptions.pngInterlaced;
        pngSaveOptions.transparency = exportOptions.pngTransparency;
        doc.saveAs(pngFile, pngSaveOptions, true, Extension.LOWERCASE);
    }
}

// 保存为TIF
function saveAsTIF(doc, file) {
    try {
        var tifFile = new File(file.toString() + ".tif");
        var tifSaveOptions = new TiffSaveOptions();
        tifSaveOptions.embedColorProfile = exportOptions.tifIcc;
        tifSaveOptions.imageCompression = exportOptions.tifCompression;
        tifSaveOptions.byteOrder = ByteOrder.IBM;

        // 如果使用JPEG压缩，设置品质
        if (exportOptions.tifCompression === TIFFEncoding.JPEG) {
            var ratio = 12 / 100;
            tifSaveOptions.jpegQuality = Math.round(exportOptions.tifQuality * ratio);
        }

        // 只有在有多个图层时才设置图层压缩
        if (doc.layers.length > 1) {
            tifSaveOptions.layerCompression = LayerCompression.RLE;
        }

        tifSaveOptions.saveImagePyramid = false;
        tifSaveOptions.transparency = exportOptions.tifTransparency;
        tifSaveOptions.alphaChannels = exportOptions.tifTransparency;
        tifSaveOptions.layers = false; // 拼合图层

        // 设置位深度
        try {
            doc.bitsPerChannel = exportOptions.tifBitDepth;
        } catch (e) {
            // 位深度转换失败时使用默认值
        }

        doc.saveAs(tifFile, tifSaveOptions, true, Extension.LOWERCASE);
    } catch (e) {
        // 如果TIF保存失败，尝试简化选项重新保存
        try {
            var tifFile = new File(file.toString() + ".tif");
            var simpleTifOptions = new TiffSaveOptions();
            simpleTifOptions.embedColorProfile = true;
            simpleTifOptions.imageCompression = TIFFEncoding.NONE;
            simpleTifOptions.byteOrder = ByteOrder.IBM;
            doc.saveAs(tifFile, simpleTifOptions, true, Extension.LOWERCASE);
        } catch (e2) {
            throw new Error("TIF保存失败: " + e2);
        }
    }
}

// 保存为PSD
function saveAsPSD(doc, file) {
    var psdFile = new File(file.toString() + ".psd");
    var psdSaveOptions = new PhotoshopSaveOptions();
    psdSaveOptions.layers = exportOptions.psdLayers;
    psdSaveOptions.embedColorProfile = true;
    psdSaveOptions.alphaChannels = true;
    psdSaveOptions.annotations = false;
    psdSaveOptions.spotColors = false;

    doc.saveAs(psdFile, psdSaveOptions, true, Extension.LOWERCASE);
}

// 保存为PDF
function saveAsPDF(doc, file) {
    var pdfFile = new File(file.toString() + ".pdf");
    var pdfSaveOptions = new PDFSaveOptions();

    // PDF标准
    var standardTypes = [
        PDFStandard.NONE,
        PDFStandard.PDFX1A2001,
        PDFStandard.PDFX1A2003,
        PDFStandard.PDFX32002,
        PDFStandard.PDFX32003,
        PDFStandard.PDFX42008
    ];
    pdfSaveOptions.PDFStandard = standardTypes[exportOptions.pdfStandard];

    // PDF兼容性
    var compatibilityTypes = [
        PDFCompatibility.PDF13,
        PDFCompatibility.PDF14,
        PDFCompatibility.PDF15,
        PDFCompatibility.PDF16,
        PDFCompatibility.PDF17
    ];
    pdfSaveOptions.PDFCompatibility = compatibilityTypes[exportOptions.pdfCompatibility];

    // 编码方式
    var encodingTypes = [
        PDFEncoding.NONE,
        PDFEncoding.PDFZIP,
        PDFEncoding.JPEG
    ];
    pdfSaveOptions.encoding = encodingTypes[exportOptions.pdfEncoding];

    // JPEG品质
    if (exportOptions.pdfEncoding === 2) { // JPEG编码
        var ratio = 12 / 100;
        pdfSaveOptions.jpegQuality = Math.round(exportOptions.pdfQuality * ratio);
    }

    pdfSaveOptions.alphaChannels = exportOptions.pdfAlphaChannel;
    pdfSaveOptions.embedColorProfile = exportOptions.pdfIcc;
    pdfSaveOptions.layers = false; // 拼合图层
    pdfSaveOptions.colorConversion = exportOptions.pdfColorConversion;
    pdfSaveOptions.profileInclusionPolicy = false;
    pdfSaveOptions.optimizeForWeb = true;
    pdfSaveOptions.view = false;
    pdfSaveOptions.convertToEightBit = true;

    doc.saveAs(pdfFile, pdfSaveOptions, true, Extension.LOWERCASE);
}
// 执行主函数
main();
