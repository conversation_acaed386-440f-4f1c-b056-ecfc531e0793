// 主函数，创建并显示UI窗口
function createMainWindow() {
    // 创建主对话框
    var mainWindow = new Window('dialog');
    mainWindow.text = '变量魔术师      原作者：WIIMOO  二次修改：maples';
    mainWindow.orientation = 'column';
    mainWindow.alignChildren = ['center', 'top'];
    mainWindow.spacing = 10;
    mainWindow.margins = 16;

    // Base64 编码的 PNG 图片数据 (可能是一个logo或装饰图)
    var encodedImage1 = '%C2%89PNG%0D%0A%1A%0A%00%00%00%0DIHDR%00%00%01%2C%00%00%00%03%08%06%00%00%00)%C3%B02%22%00%00%00%09pHYs%00%00%0B%13%00%00%0B%13%01%00%C2%9A%C2%9C%18%00%00%09%03iTXtXML%3Acom.adobe.xmp%00%00%00%00%00%3C%3Fxpacket%20begin%3D%22%C3%AF%C2%BB%C2%BF%22%20id%3D%22W5M0MpCehiHzreSzNTczkc9d%22%3F%3E%20%3Cx%3Axmpmeta%20xmlns%3Ax%3D%22adobe%3Ans%3Ameta%2F%22%20x%3Axmptk%3D%22Adobe%20XMP%20Core%207.1-c000%2079.b0f8be9%2C%202021%2F12%2F08-19%3A11%3A22%20%20%20%20%20%20%20%20%22%3E%20%3Crdf%3ARDF%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%3E%20%3Crdf%3ADescription%20rdf%3Aabout%3D%22%22%20xmlns%3Axmp%3D%22http%3A%2F%2Fns.adobe.com%2Fxap%2F1.0%2F%22%20xmlns%3AxmpMM%3D%22http%3A%2F%2Fns.adobe.com%2Fxap%2F1.0%2Fmm%2F%22%20xmlns%3AstEvt%3D%22http%3A%2F%2Fns.adobe.com%2Fxap%2F1.0%2FsType%2FResourceEvent%23%22%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Aphotoshop%3D%22http%3A%2F%2Fns.adobe.com%2Fphotoshop%2F1.0%2F%22%20xmp%3ACreatorTool%3D%22Adobe%20Photoshop%2023.2%20(Windows)%22%20xmp%3ACreateDate%3D%222024-04-04T12%3A47%3A45%2B08%3A00%22%20xmp%3AMetadataDate%3D%222024-04-04T12%3A47%3A45%2B08%3A00%22%20xmp%3AModifyDate%3D%222024-04-04T12%3A47%3A45%2B08%3A00%22%20xmpMM%3AInstanceID%3D%22xmp.iid%3A6b7c5764-78c6-154f-9ffc-85ef49f49052%22%20xmpMM%3ADocumentID%3D%22adobe%3Adocid%3Aphotoshop%3Ac23f4d4a-d8a7-e047-bee6-266c91259936%22%20xmpMM%3AOriginalDocumentID%3D%22xmp.did%3A8d3237b9-7dbe-1644-9456-583bce01111b%22%20dc%3Aformat%3D%22image%2Fpng%22%20photoshop%3AHistory%3D%222024-04-04T12%3A47%3A35%2B08%3A00%26%23x9%3B%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%20%C3%A6%C2%9C%C2%AA%C3%A6%C2%A0%C2%87%C3%A9%C2%A2%C2%98-1%20%C3%A5%C2%B7%C2%B2%C3%A6%C2%89%C2%93%C3%A5%C2%BC%C2%80%26%23xA%3B%C3%A5%C2%BB%C2%BA%C3%A7%C2%AB%C2%8B%3A%26%23x9%3B%C3%A6%C2%96%C2%B0%C3%A5%C2%BB%C2%BA%3A%20%C3%A6%C2%96%C2%87%C3%A6%C2%A1%C2%A3%26%23xA%3BFALSE%26%23xA%3BFALSE%26%23xA%3B%C3%A6%C2%A8%C2%A1%C3%A5%C2%BC%C2%8F%3A%20RGB%20%C3%A9%C2%A2%C2%9C%C3%A8%C2%89%C2%B2%C3%A6%C2%A8%C2%A1%C3%A5%C2%BC%C2%8F%26%23xA%3B%C3%A5%C2%AE%C2%BD%C3%A5%C2%BA%C2%A6%3A%2010.58%20%C3%A5%C2%8E%C2%98%C3%A7%C2%B1%C2%B3%26%23xA%3B%C3%A9%C2%AB%C2%98%C3%A5%C2%BA%C2%A6%3A%200.11%20%C3%A5%C2%8E%C2%98%C3%A7%C2%B1%C2%B3%26%23xA%3B%C3%A6%C2%AF%C2%8F%C3%A5%C2%8E%C2%98%C3%A7%C2%B1%C2%B3%20%C3%A5%C2%88%C2%86%C3%A8%C2%BE%C2%A8%C3%A7%C2%8E%C2%87%3A%2028.346%26%23xA%3B%C3%A5%C2%83%C2%8F%C3%A7%C2%B4%C2%A0%C3%A9%C2%95%C2%BF%C3%A5%C2%AE%C2%BD%C3%A6%C2%AF%C2%94%3A%201%26%23xA%3B%C3%A5%C2%A1%C2%AB%C3%A5%C2%85%C2%85%3A%20%C3%A7%C2%99%C2%BD%C3%A8%C2%89%C2%B2%26%23xA%3B%C3%A6%C2%B7%C2%B1%C3%A5%C2%BA%C2%A6%3A%208%26%23xA%3B%C3%A9%C2%85%C2%8D%C3%A7%C2%BD%C2%AE%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%3A%20%C3%A2%C2%80%C2%9CsRGB%20IEC61966-2.1%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A5%C2%8F%C2%82%C3%A8%C2%80%C2%83%C3%A7%C2%BA%C2%BF%3A%20%C3%A6%C2%97%C2%A0%26%23xA%3B219%26%23xA%3B%26%23xA%3B%C3%A5%C2%BB%C2%BA%C3%A7%C2%AB%C2%8B%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%26%23xA%3B%C3%A8%C2%AE%C2%BE%C3%A7%C2%BD%C2%AE%20%C3%A8%C2%83%C2%8C%C3%A6%C2%99%C2%AF%26%23x9%3B%C3%A4%C2%B8%C2%BA%3A%20%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%26%23xA%3B%C3%A4%C2%B8%C2%8D%C3%A9%C2%80%C2%8F%C3%A6%C2%98%C2%8E%C3%A5%C2%BA%C2%A6%3A%20100%25%26%23xA%3B%C3%A6%C2%A8%C2%A1%C3%A5%C2%BC%C2%8F%3A%20%C3%A6%C2%AD%C2%A3%C3%A5%C2%B8%C2%B8%26%23xA%3B2%26%23xA%3B%26%23xA%3B%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%C3%A5%C2%8F%C2%AF%C3%A8%C2%A7%C2%81%C3%A6%C2%80%C2%A7%26%23xA%3B%C3%A9%C2%9A%C2%90%C3%A8%C2%97%C2%8F%20%C3%A5%C2%BD%C2%93%C3%A5%C2%89%C2%8D%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%26%23x9%3B%26%23xA%3B2024-04-04T12%3A47%3A45%2B08%3A00%26%23x9%3B%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%20D%3A%5CUsers%5CDesktop%5C%C3%A6%C2%9C%C2%AA%C3%A6%C2%A0%C2%87%C3%A9%C2%A2%C2%98-1.png%20%C3%A5%C2%B7%C2%B2%C3%A5%C2%AD%C2%98%C3%A5%C2%82%C2%A8%26%23xA%3B%C3%A5%C2%AD%C2%98%C3%A5%C2%82%C2%A8%26%23x9%3B%C3%A4%C2%B8%C2%BA%3A%20PNG%26%23xA%3B%C3%A4%C2%BC%C2%98%C3%A5%C2%8C%C2%96%C3%A6%C2%96%C2%B9%C3%A6%C2%B3%C2%95%3A%20%C3%A5%C2%BF%C2%AB%C3%A9%C2%80%C2%9F%26%23xA%3B%C3%A4%C2%BA%C2%A4%C3%A9%C2%94%C2%99%3A%20%C3%A6%C2%97%C2%A0%26%23xA%3B%C3%A6%C2%BB%C2%A4%C3%A9%C2%95%C2%9C%3A%20%C3%A9%C2%9A%C2%8F%C3%A6%C2%A0%C2%B7%C3%A6%C2%80%C2%A7%26%23xA%3B%C3%A5%C2%8E%C2%8B%C3%A7%C2%BC%C2%A9%3A%206%26%23xA%3B%C3%A5%C2%9C%C2%A8%3A%20D%3A%5CUsers%5CDesktop%5C%26%23xA%3B219%26%23xA%3B%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9C%C3%A6%C2%8B%C2%B7%C3%A8%C2%B4%C2%9D%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9C%C3%A5%C2%B0%C2%8F%C3%A5%C2%86%C2%99%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A4%C2%B8%C2%8D%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9CICC%20%C3%A9%C2%85%C2%8D%C3%A7%C2%BD%C2%AE%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%C3%A2%C2%80%C2%9D%26%23xA%3B%26%23xA%3B%22%20photoshop%3AColorMode%3D%223%22%3E%20%3CxmpMM%3AHistory%3E%20%3Crdf%3ASeq%3E%20%3Crdf%3Ali%20stEvt%3Aaction%3D%22created%22%20stEvt%3AinstanceID%3D%22xmp.iid%3A8d3237b9-7dbe-1644-9456-583bce01111b%22%20stEvt%3Awhen%3D%222024-04-04T12%3A47%3A45%2B08%3A00%22%20stEvt%3AsoftwareAgent%3D%22Adobe%20Photoshop%2023.2%20(Windows)%22%2F%3E%20%3Crdf%3Ali%20stEvt%3Aaction%3D%22saved%22%20stEvt%3AinstanceID%3D%22xmp.iid%3A6b7c5764-78c6-154f-9ffc-85ef49f49052%22%20stEvt%3Awhen%3D%222024-04-04T12%3A47%3A45%2B08%3A00%22%20stEvt%3AsoftwareAgent%3D%22Adobe%20Photoshop%2023.2%20(Windows)%22%20stEvt%3Achanged%3D%22%2F%22%2F%3E%20%3C%2Frdf%3ASeq%3E%20%3C%2FxmpMM%3AHistory%3E%20%3C%2Frdf%3ADescription%3E%20%3C%2Frdf%3ARDF%3E%20%3C%2Fx%3Axmpmeta%3E%20%3C%3Fxpacket%20end%3D%22r%22%3F%3E%C2%A9%C3%93.%C3%81%00%00%00)IDATH%C2%89%C3%AD%C3%94%C2%B1%0D%000%0C%C3%83%C2%B04%C3%BF%C3%BF%C3%AC%C2%BE%C3%90%C2%AD0%40%5E%C2%A0I\'%C3%89%004%C3%98%C3%9F%01%00%C2%AF%0C%0B%C2%A8q%01C%C2%8F%03%03%10%C2%B8%C2%AFl%00%00%00%00IEND%C2%AEB%60%C2%82';
    var image1 = mainWindow.add('image', undefined, File.decode(encodedImage1), { name: 'image1' });
    image1.alignment = ['center', 'top'];

    // --- 文件选择面板 ---
    var fileSelectionGroup = mainWindow.add('group', undefined, { name: 'group1' });
    fileSelectionGroup.orientation = 'row';
    fileSelectionGroup.alignChildren = ['left', 'center'];
    fileSelectionGroup.spacing = 10;
    fileSelectionGroup.margins = 0;

    var fileSelectionPanel = fileSelectionGroup.add('panel', undefined, undefined, { name: 'panel1' });
    fileSelectionPanel.text = '文件选择(CSV文件和模板目录在同一目录可不指定模板目录 )';
    fileSelectionPanel.orientation = 'column';
    fileSelectionPanel.alignChildren = ['left', 'top'];
    fileSelectionPanel.spacing = 10;
    fileSelectionPanel.margins = 10;

    // 数据文件 (CSV)
    var dataFileGroup = fileSelectionPanel.add('group', undefined, { name: 'group2' });
    dataFileGroup.orientation = 'row';
    dataFileGroup.alignChildren = ['left', 'center'];
    dataFileGroup.spacing = 10;
    dataFileGroup.margins = 0;
    dataFileGroup.add('statictext', undefined, '数据文件', { name: 'statictext1' });
    var editDataFile = dataFileGroup.add('edittext {properties: {name: "datafile"}}');
    editDataFile.preferredSize.width = 200;
    var btnSelectDataFile = dataFileGroup.add('button', undefined, '选择', { name: 'btndata' });

    // 模板目录
    var templateDirGroup = fileSelectionPanel.add('group', undefined, { name: 'group3' });
    templateDirGroup.orientation = 'row';
    templateDirGroup.alignChildren = ['left', 'center'];
    templateDirGroup.spacing = 10;
    templateDirGroup.margins = 0;
    templateDirGroup.add('statictext', undefined, '模版目录', { name: 'statictext2' });
    var editTemplateDir = templateDirGroup.add('edittext {properties: {name: "datamoban"}}');
    editTemplateDir.text = '默认PS打开文档为模板文件';
    editTemplateDir.preferredSize.width = 200;
    var btnSelectTemplateDir = templateDirGroup.add('button', undefined, '选择', { name: 'btnmoban' });

    // 图片目录
    var imageDirGroup = fileSelectionPanel.add('group', undefined, { name: 'group4' });
    imageDirGroup.orientation = 'row';
    imageDirGroup.alignChildren = ['left', 'center'];
    imageDirGroup.spacing = 10;
    imageDirGroup.margins = 0;
    imageDirGroup.add('statictext', undefined, '图片目录', { name: 'statictext3' });
    var editImageDir = imageDirGroup.add('edittext {properties: {name: "dataimg"}}');
    editImageDir.text = '不指定目录,默认表格内容为全路径';
    editImageDir.preferredSize.width = 200;
    var btnSelectImageDir = imageDirGroup.add('button', undefined, '选择', { name: 'btnimg' });

    // 导出目录
    var savePathGroup = fileSelectionPanel.add('group', undefined, { name: 'group5' });
    savePathGroup.orientation = 'row';
    savePathGroup.alignChildren = ['left', 'center'];
    savePathGroup.spacing = 10;
    savePathGroup.margins = 0;
    savePathGroup.add('statictext', undefined, '导出目录', { name: 'statictext4' });
    var editSavePath = savePathGroup.add('edittext {properties: {name: "editsavepath"}}');
    editSavePath.text = '不指定目录,默认导出到数据文件夹下';
    editSavePath.preferredSize.width = 200;
    var btnSelectSavePath = savePathGroup.add('button', undefined, '选择', { name: 'btnsave' });

    // --- 设置面板 ---
    var settingsGroup = mainWindow.add('group', undefined, { name: 'group6' });
    settingsGroup.orientation = 'row';
    settingsGroup.alignChildren = ['left', 'center'];
    settingsGroup.spacing = 10;
    settingsGroup.margins = 0;

    var settingsPanel = settingsGroup.add('panel', undefined, undefined, { name: 'panel2' });
    settingsPanel.orientation = 'column';
    settingsPanel.alignChildren = ['left', 'top'];
    settingsPanel.spacing = 10;
    settingsPanel.margins = 10;

    // 变量设置
    var variableSetGroup = settingsPanel.add('group', undefined, { name: 'group7' });
    variableSetGroup.orientation = 'row';
    variableSetGroup.alignChildren = ['left', 'center'];
    variableSetGroup.spacing = 10;
    variableSetGroup.margins = 0;
    variableSetGroup.add('statictext', undefined, '变量', { name: 'statictext5' });
    var editVariableSet = variableSetGroup.add('edittext {properties: {name: "bianliangset"}}');
    editVariableSet.preferredSize.width = 305;

    // 模板设置
    var templateSetGroup = settingsPanel.add('group', undefined, { name: 'group8' });
    templateSetGroup.orientation = 'row';
    templateSetGroup.alignChildren = ['left', 'center'];
    templateSetGroup.spacing = 10;
    templateSetGroup.margins = 0;
    templateSetGroup.add('statictext', undefined, '模版', { name: 'statictext6' });
    var editTemplateSet = templateSetGroup.add('edittext {properties: {name: "mobanset"}}');
    editTemplateSet.preferredSize.width = 305;

    // 文件名设置
    var filenameSetGroup = settingsPanel.add('group', undefined, { name: 'group9' });
    filenameSetGroup.orientation = 'row';
    filenameSetGroup.alignChildren = ['left', 'center'];
    filenameSetGroup.spacing = 10;
    filenameSetGroup.margins = 0;
    filenameSetGroup.add('statictext', undefined, '文件名', { name: 'statictext7' });
    var editFilenameSet = filenameSetGroup.add('edittext {properties: {name: "filenameset"}}');
    editFilenameSet.preferredSize.width = 305;

    // 文件名快捷按钮
    var filenameButtonsGroup = settingsPanel.add('group', undefined, { name: 'group10' });
    filenameButtonsGroup.orientation = 'row';
    filenameButtonsGroup.alignChildren = ['center', 'center'];
    filenameButtonsGroup.spacing = 10;
    filenameButtonsGroup.margins = 0;
    filenameButtonsGroup.alignment = ['center', 'top'];
    var btnTime = filenameButtonsGroup.add('button', undefined, '时间', { name: 'btntime' });
    var btnFilename = filenameButtonsGroup.add('button', undefined, '文件名', { name: 'btnfilename' });
    btnFilename.helpTip = '当前打开文件名';
    var btnDate = filenameButtonsGroup.add('button', undefined, '日期', { name: 'btndate' });
    var btnVariable = filenameButtonsGroup.add('button', undefined, '变量', { name: 'btnbl' });
    btnVariable.helpTip = '请手动输入数据列标题';
    var btnId = filenameButtonsGroup.add('button', undefined, '编号', { name: 'btnid' });

    // --- 导出设置面板 ---
    var exportPanel = mainWindow.add('panel', undefined, undefined, { name: 'panel3' });
    exportPanel.orientation = 'column';
    exportPanel.alignChildren = ['left', 'top'];
    exportPanel.spacing = 10;
    exportPanel.margins = 10;

    // 缩放方式
    var scalingGroup = exportPanel.add('group', undefined, { name: 'group11' });
    scalingGroup.orientation = 'row';
    scalingGroup.alignChildren = ['left', 'center'];
    scalingGroup.spacing = 10;
    scalingGroup.margins = 0;
    scalingGroup.add('statictext', undefined, '缩放', { name: 'statictext8', helpTip: '图片缩放方式' });
    var rbtnStretch = scalingGroup.add('radiobutton', undefined, '拉伸', { name: 'rbtnsuofang' });
    rbtnStretch.helpTip = '变形填充满原图片';
    rbtnStretch.value = true;
    var rbtnFitOverflow = scalingGroup.add('radiobutton', undefined, '等比溢出', { name: 'rbtndengbiyichu' });
    rbtnFitOverflow.helpTip = '保持比例且填满原图片';
    var rbtnFitFill = scalingGroup.add('radiobutton', undefined, '等比填充', { name: 'rbntdengbitianchong' });
    rbtnFitFill.helpTip = '保持比例，最大不超出原图片';
    var rbtnNoScale = scalingGroup.add('radiobutton', undefined, '不缩放', { name: 'rbtnno' });
    rbtnNoScale.helpTip = '保持原图大小不变';

    // 导出格式
    var formatGroup = exportPanel.add('group', undefined, { name: 'group12' });
    formatGroup.orientation = 'row';
    formatGroup.alignChildren = ['left', 'center'];
    formatGroup.spacing = 10;
    formatGroup.margins = 0;
    var ckPsd = formatGroup.add('checkbox', undefined, 'PSD', { name: 'ckpsd' });
    var ckTiff = formatGroup.add('checkbox', undefined, 'TIFF', { name: 'cktiff' });
    var ckPdf = formatGroup.add('checkbox', undefined, 'PDF', { name: 'ckpdf' });
    var ckPng = formatGroup.add('checkbox', undefined, 'PNG', { name: 'ckpng' });
    var ckJpeg = formatGroup.add('checkbox', undefined, 'JPEG', { name: 'ckjpeg' });
    ckJpeg.value = true;
    var ckWeb = formatGroup.add('checkbox', undefined, 'WEB', { name: 'ckweb' });
    ckWeb.value = true;

    // --- 主控制按钮 ---
    var mainControlGroup = mainWindow.add('group', undefined, { name: 'group13' });
    mainControlGroup.orientation = 'row';
    mainControlGroup.alignChildren = ['left', 'center'];
    mainControlGroup.spacing = 10;
    mainControlGroup.margins = 0;
    var btnHelp = mainControlGroup.add('button', undefined, '帮助', { name: 'btnhelp' });
    var btnClear = mainControlGroup.add('button', undefined, '清空设置', { name: 'btnclear' });
    var btnCancel = mainControlGroup.add('button', undefined, '取消', { name: 'btncacle' });
    var btnOk = mainControlGroup.add('button', undefined, '确定', { name: 'btnok' });

    // 第二个 Base64 编码的图片
    var encodedImage2 = '%C2%89PNG%0D%0A%1A%0A%00%00%00%0DIHDR%00%00%01%2C%00%00%00%05%08%06%00%00%00%C3%BF%C2%A9%C3%91%3F%00%00%00%09pHYs%00%00%0B%13%00%00%0B%13%01%00%C2%9A%C2%9C%18%00%00%0A%C2%8DiTXtXML%3Acom.adobe.xmp%00%00%00%00%00%3C%3Fxpacket%20begin%3D%22%C3%AF%C2%BB%C2%BF%22%20id%3D%22W5M0MpCehiHzreSzNTczkc9d%22%3F%3E%20%3Cx%3Axmpmeta%20xmlns%3Ax%3D%22adobe%3Ans%3Ameta%2F%22%20x%3Axmptk%3D%22Adobe%20XMP%20Core%207.1-c000%2079.b0f8be9%2C%202021%2F12%2F08-19%3A11%3A22%20%20%20%20%20%20%20%20%22%3E%20%3Crdf%3ARDF%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%3E%20%3Crdf%3ADescription%20rdf%3Aabout%3D%22%22%20xmlns%3Axmp%3D%22http%3A%2F%2Fns.adobe.com%2Fxap%2F1.0%2F%22%20xmlns%3AxmpMM%3D%22http%3A%2F%2Fns.adobe.com%2Fxap%2F1.0%2Fmm%2F%22%20xmlns%3AstEvt%3D%22http%3A%2F%2Fns.adobe.com%2Fxap%2F1.0%2FsType%2FResourceEvent%23%22%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Aphotoshop%3D%22http%3A%2F%2Fns.adobe.com%2Fphotoshop%2F1.0%2F%22%20xmp%3ACreatorTool%3D%22Adobe%20Photoshop%2023.2%20(Windows)%22%20xmp%3ACreateDate%3D%222024-04-04T12%3A49%3A49%2B08%3A00%22%20xmp%3AMetadataDate%3D%222024-04-04T12%3A49%3A49%2B08%3A00%22%20xmp%3AModifyDate%3D%222024-04-04T12%3A49%3A49%2B08%3A00%22%20xmpMM%3AInstanceID%3D%22xmp.iid%3Ac9c6d915-c670-a645-a889-e530cc9e3014%22%20xmpMM%3ADocumentID%3D%22adobe%3Adocid%3Aphotoshop%3Ae055f9d2-fbd3-2b4b-904e-ad77c3593c0c%22%20xmpMM%3AOriginalDocumentID%3D%22xmp.did%3A3f01b3b5-c0a2-0244-8cbf-6bfbfb7fd5b5%22%20dc%3Aformat%3D%22image%2Fpng%22%20photoshop%3AHistory%3D%222024-04-04T12%3A47%3A35%2B08%3A00%26%23x9%3B%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%20%C3%A6%C2%9C%C2%AA%C3%A6%C2%A0%C2%87%C3%A9%C2%A2%C2%98-1%20%C3%A5%C2%B7%C2%B2%C3%A6%C2%89%C2%93%C3%A5%C2%BC%C2%80%26%23xA%3B%C3%A5%C2%BB%C2%BA%C3%A7%C2%AB%C2%8B%3A%26%23x9%3B%C3%A6%C2%96%C2%B0%C3%A5%C2%BB%C2%BA%3A%20%C3%A6%C2%96%C2%87%C3%A6%C2%A1%C2%A3%26%23xA%3BFALSE%26%23xA%3BFALSE%26%23xA%3B%C3%A6%C2%A8%C2%A1%C3%A5%C2%BC%C2%8F%3A%20RGB%20%C3%A9%C2%A2%C2%9C%C3%A8%C2%89%C2%B2%C3%A6%C2%A8%C2%A1%C3%A5%C2%BC%C2%8F%26%23xA%3B%C3%A5%C2%AE%C2%BD%C3%A5%C2%BA%C2%A6%3A%2010.58%20%C3%A5%C2%8E%C2%98%C3%A7%C2%B1%C2%B3%26%23xA%3B%C3%A9%C2%AB%C2%98%C3%A5%C2%BA%C2%A6%3A%200.11%20%C3%A5%C2%8E%C2%98%C3%A7%C2%B1%C2%B3%26%23xA%3B%C3%A6%C2%AF%C2%8F%C3%A5%C2%8E%C2%98%C3%A7%C2%B1%C2%B3%20%C3%A5%C2%88%C2%86%C3%A8%C2%BE%C2%A8%C3%A7%C2%8E%C2%87%3A%2028.346%26%23xA%3B%C3%A5%C2%83%C2%8F%C3%A7%C2%B4%C2%A0%C3%A9%C2%95%C2%BF%C3%A5%C2%AE%C2%BD%C3%A6%C2%AF%C2%94%3A%201%26%23xA%3B%C3%A5%C2%A1%C2%AB%C3%A5%C2%85%C2%85%3A%20%C3%A7%C2%99%C2%BD%C3%A8%C2%89%C2%B2%26%23xA%3B%C3%A6%C2%B7%C2%B1%C3%A5%C2%BA%C2%A6%3A%208%26%23xA%3B%C3%A9%C2%85%C2%8D%C3%A7%C2%BD%C2%AE%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%3A%20%C3%A2%C2%80%C2%9CsRGB%20IEC61966-2.1%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A5%C2%8F%C2%82%C3%A8%C2%80%C2%83%C3%A7%C2%BA%C2%BF%3A%20%C3%A6%C2%97%C2%A0%26%23xA%3B219%26%23xA%3B%26%23xA%3B%C3%A5%C2%BB%C2%BA%C3%A7%C2%AB%C2%8B%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%26%23xA%3B%C3%A8%C2%AE%C2%BE%C3%A7%C2%BD%C2%AE%20%C3%A8%C2%83%C2%8C%C3%A6%C2%99%C2%AF%26%23x9%3B%C3%A4%C2%B8%C2%BA%3A%20%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%26%23xA%3B%C3%A4%C2%B8%C2%8D%C3%A9%C2%80%C2%8F%C3%A6%C2%98%C2%8E%C3%A5%C2%BA%C2%A6%3A%20100%25%26%23xA%3B%C3%A6%C2%A8%C2%A1%C3%A5%C2%BC%C2%8F%3A%20%C3%A6%C2%AD%C2%A3%C3%A5%C2%B8%C2%B8%26%23xA%3B2%26%23xA%3B%26%23xA%3B%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%C3%A5%C2%8F%C2%AF%C3%A8%C2%A7%C2%81%C3%A6%C2%80%C2%A7%26%23xA%3B%C3%A9%C2%9A%C2%90%C3%A8%C2%97%C2%8F%20%C3%A5%C2%BD%C2%93%C3%A5%C2%89%C2%8D%C3%A5%C2%9B%C2%BE%C3%A5%C2%B1%C2%82%26%23x9%3B%26%23xA%3B2024-04-04T12%3A47%3A45%2B08%3A00%26%23x9%3B%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%20D%3A%5CUsers%5CDesktop%5C%C3%A6%C2%9C%C2%AA%C3%A6%C2%A0%C2%87%C3%A9%C2%A2%C2%98-1.png%20%C3%A5%C2%B7%C2%B2%C3%A5%C2%AD%C2%98%C3%A5%C2%82%C2%A8%26%23xA%3B%C3%A5%C2%AD%C2%98%C3%A5%C2%82%C2%A8%26%23x9%3B%C3%A4%C2%B8%C2%BA%3A%20PNG%26%23xA%3B%C3%A4%C2%BC%C2%98%C3%A5%C2%8C%C2%96%C3%A6%C2%96%C2%B9%C3%A6%C2%B3%C2%95%3A%20%C3%A5%C2%BF%C2%AB%C3%A9%C2%80%C2%9F%26%23xA%3B%C3%A4%C2%BA%C2%A4%C3%A9%C2%94%C2%99%3A%20%C3%A6%C2%97%C2%A0%26%23xA%3B%C3%A6%C2%BB%C2%A4%C3%A9%C2%95%C2%9C%3A%20%C3%A9%C2%9A%C2%8F%C3%A6%C2%A0%C2%B7%C3%A6%C2%80%C2%A7%26%23xA%3B%C3%A5%C2%8E%C2%8B%C3%A7%C2%BC%C2%A9%3A%206%26%23xA%3B%C3%A5%C2%9C%C2%A8%3A%20D%3A%5CUsers%5CDesktop%5C%26%23xA%3B219%26%23xA%3B%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9C%C3%A6%C2%8B%C2%B7%C3%A8%C2%B4%C2%9D%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9C%C3%A5%C2%B0%C2%8F%C3%A5%C2%86%C2%99%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A4%C2%B8%C2%8D%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9CICC%20%C3%A9%C2%85%C2%8D%C3%A7%C2%BD%C2%AE%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%C3%A2%C2%80%C2%9D%26%23xA%3B%26%23xA%3B%C3%A7%C2%94%C2%BB%C3%A5%C2%B8%C2%83%C3%A5%C2%A4%C2%A7%C3%A5%C2%B0%C2%8F%26%23xA%3B%C3%A7%C2%94%C2%BB%C3%A5%C2%B8%C2%83%C3%A5%C2%A4%C2%A7%C3%A5%C2%B0%C2%8F%26%23x9%3B%C3%A9%C2%AB%C2%98%C3%A5%C2%BA%C2%A6%3A%205%20%C3%A5%C2%83%C2%8F%C3%A7%C2%B4%C2%A0%26%23xA%3B%C3%A5%C2%9E%C2%82%C3%A7%C2%9B%C2%B4%3A%20%C3%A5%C2%B1%C2%85%C3%A4%C2%B8%C2%AD%26%23xA%3B%26%23xA%3B2024-04-04T12%3A49%3A49%2B08%3A00%26%23x9%3B%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%20D%3A%5CUsers%5CDesktop%5C%C3%A6%C2%9C%C2%AA%C3%A6%C2%A0%C2%87%C3%A9%C2%A2%C2%98-11.png%20%C3%A5%C2%B7%C2%B2%C3%A5%C2%AD%C2%98%C3%A5%C2%82%C2%A8%26%23xA%3B%C3%A5%C2%AD%C2%98%C3%A5%C2%82%C2%A8%26%23x9%3B%C3%A4%C2%B8%C2%BA%3A%20PNG%26%23xA%3B%C3%A4%C2%BC%C2%98%C3%A5%C2%8C%C2%96%C3%A6%C2%96%C2%B9%C3%A6%C2%B3%C2%95%3A%20%C3%A5%C2%BF%C2%AB%C3%A9%C2%80%C2%9F%26%23xA%3B%C3%A4%C2%BA%C2%A4%C3%A9%C2%94%C2%99%3A%20%C3%A6%C2%97%C2%A0%26%23xA%3B%C3%A6%C2%BB%C2%A4%C3%A9%C2%95%C2%9C%3A%20%C3%A9%C2%9A%C2%8F%C3%A6%C2%A0%C2%B7%C3%A6%C2%80%C2%A7%26%23xA%3B%C3%A5%C2%8E%C2%8B%C3%A7%C2%BC%C2%A9%3A%206%26%23xA%3B%C3%A5%C2%9C%C2%A8%3A%20D%3A%5CUsers%5CDesktop%5C%C3%A6%C2%9C%C2%AA%C3%A6%C2%A0%C2%87%C3%A9%C2%A2%C2%98-11.png%26%23xA%3B219%26%23xA%3B%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9C%C3%A6%C2%8B%C2%B7%C3%A8%C2%B4%C2%9D%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9C%C3%A5%C2%B0%C2%8F%C3%A5%C2%86%C2%99%C3%A2%C2%80%C2%9D%26%23xA%3B%C3%A4%C2%B8%C2%8D%C3%A5%C2%90%C2%AB%C3%A2%C2%80%C2%9CICC%20%C3%A9%C2%85%C2%8D%C3%A7%C2%BD%C2%AE%C3%A6%C2%96%C2%87%C3%A4%C2%BB%C2%B6%C3%A2%C2%80%C2%9D%26%23xA%3B%26%23xA%3B%22%20photoshop%3AColorMode%3D%223%22%3E%20%3CxmpMM%3AHistory%3E%20%3Crdf%3ASeq%3E%20%3Crdf%3Ali%20stEvt%3Aaction%3D%22created%22%20stEvt%3AinstanceID%3D%22xmp.iid%3A3f01b3b5-c0a2-0244-8cbf-6bfbfb7fd5b5%22%20stEvt%3Awhen%3D%222024-04-04T12%3A49%3A49%2B08%3A00%22%20stEvt%3AsoftwareAgent%3D%22Adobe%20Photoshop%2023.2%20(Windows)%22%2F%3E%20%3Crdf%3Ali%20stEvt%3Aaction%3D%22saved%22%20stEvt%3AinstanceID%3D%22xmp.iid%3Ac9c6d915-c670-a645-a889-e530cc9e3014%22%20stEvt%3Awhen%3D%222024-04-04T12%3A49%3A49%2B08%3A00%22%20stEvt%3AsoftwareAgent%3D%22Adobe%20Photoshop%2023.2%20(Windows)%22%20stEvt%3Achanged%3D%22%2F%22%2F%3E%20%3C%2Frdf%3ASeq%3E%20%3C%2FxmpMM%3AHistory%3E%20%3C%2Frdf%3ADescription%3E%20%3C%2Frdf%3ARDF%3E%20%3C%2Fx%3Axmpmeta%3E%20%3C%3Fxpacket%20end%3D%22r%22%3F%3ES%40%C2%B7k%00%00%00%2FIDATX%C2%85%C3%AD%C3%94%C2%B1%0D%000%0C%C3%83%C2%B0%C2%B6%C3%BF%C3%BF%C3%AC%C2%BE%C2%90-0%40%5E%C2%A0I7%C3%89%01h%C3%B0%C2%B6%03%00%C2%A6%0C%0B%C2%A8aX%40%0D%C3%83%02j%7C%C2%A3%7F%03%07G%C2%A8%C3%805%00%00%00%00IEND%C2%AEB%60%C2%82';
    var image2 = mainWindow.add('image', undefined, File.decode(encodedImage2), { name: 'image2' });
    image2.alignment = ['center', 'top'];

    // --- 全局变量 ---
    var csvFile; // 数据文件 (CSV)
    var templateFolder; // 模板文件夹
    var csvHeaders; // CSV文件的表头
    var csvDataRows = []; // CSV文件的数据行
    var activeTemplateDoc; // 当前活动的模板文档
    var allLayersInTemplate = []; // 模板文档中的所有图层
    var imageFolder; // 图片文件夹
    var layersToProcess = []; // 需要处理的图层
    var variableNames = []; // 变量名数组
    var templateFileName; // 模板文件名
    var layersToScan = []; // 扫描图层时使用的临时数组
    var saveFolder; // 导出文件夹
    var settingsFilePath = Folder.myDocuments + '/VariableMagician.xml'; // 配置文件路径
    var isTemplateFromActiveDoc = false; // 标记模板是否为当前打开的文档
    var savePathFolder; // 保存路径的文件夹对象

    // --- 事件处理 ---

    btnSelectSavePath.onClick = function () {
        savePathFolder = Folder.selectDialog('选择图片保存目录');
        if (savePathFolder != undefined) {
            editSavePath.text = savePathFolder.fsName;
        }
    };

    btnTime.onClick = function () {
        editFilenameSet.text = editFilenameSet.text + '%time%';
    };

    btnFilename.onClick = function () {
        editFilenameSet.text = editFilenameSet.text + '%name%';
    };

    btnDate.onClick = function () {
        editFilenameSet.text = editFilenameSet.text + '%date%';
    };

    btnVariable.onClick = function () {
        editFilenameSet.text = editFilenameSet.text + '%%';
    };

    btnId.onClick = function () {
        editFilenameSet.text = editFilenameSet.text + '%id%';
    };

    btnHelp.onClick = function () {
        alert('1.数据源为excel另存为.csv文件;\x0a2.如果当前PS有打开文件,默认当前打开文件为模板文件;3.图片和模版可指定为绝对路径,当使用相对路径时,必须指定模版和图片的目录;\x0a4.选择数据源后,程序会自动生成表格表头列为变量,模板文件中图层名和变量一至就替换数据源文本或数据源指定图片;\x0a5.模板和文件名可以自定义,数据列标题(变量)必须放%%中间如%变量%\x0a例如:自定义文件名   ‘姓名’ 列内容为 ‘张三’ , ‘年龄’ 列内容为 ‘25’ ,\x0a可自定义为’%姓名%-%年龄%‘,生成后文件名为‘张三-25’\x0a\x0a由于csv是逗号分隔的格式,如果表格中存在数据中有逗号的字符,请在保存csv之前,全局用%，%替换,(把英文逗号替换为两个百分号夹着的中文逗号),其次回车换行,请在回车换行的字符串后面,用%r%代替,程序识别到之后,在替换数据之后则会用换行处理.\x0a\x0a华印社区（52cnp.com）：WIIMOO\x0a\x0a二次修改：华印社区（52cnp.com）：maples', '使用帮助');
    };

    btnClear.onClick = function () {
        editDataFile.text = '';
        editTemplateDir.text = '默认PS打开文档为模板文件';
        editVariableSet.text = '';
        editTemplateSet.text = '';
        editFilenameSet.text = '';
        ckPsd.value = false;
        ckTiff.value = false;
        ckPng.value = false;
        ckJpeg.value = true;
        ckPdf.value = false;
        csvDataRows = [];
        templateFolder = null;
    };

    btnSelectDataFile.onClick = function () {
        csvFile = File.openDialog('请选择数据文件', 'CSV:*.csv', false);
        editVariableSet.text = '';
        if (csvFile == undefined) {
            return;
        }
        editDataFile.text = csvFile.fsName;
        var fileReader = new File(csvFile);
        var isOpen = fileReader.open('r', 'TEXT', '????');
        if (!isOpen) {
            return;
        }
        csvHeaders = fileReader.readln().split(',');
        if (csvHeaders.length < 1) {
            alert('数据缺失请确认');
            return;
        } else {
            for (var i = 0; i < csvHeaders.length; i++) {
                if (csvHeaders[i] != null) {
                    editVariableSet.text += csvHeaders[i] + ',';
                }
            }
            var line;
            var commaRegex = new RegExp('%，%', 'g');
            var newlineRegex = new RegExp('%r%', 'g');
            while (!fileReader.eof) {
                line = fileReader.readln().split(',');
                var hasContent = false;
                for (var i = 0; i < line.length; i++) {
                    if (line[i] != '') {
                        hasContent = true;
                    }
                    line[i] = line[i].replace(commaRegex, ',');
                    line[i] = line[i].replace(newlineRegex, '\r');
                }
                if (hasContent) {
                    csvDataRows.push(line);
                }
            }
        }
    };

    btnSelectTemplateDir.onClick = function () {
        templateFolder = Folder.selectDialog('请选择模板文件目录');
        if (templateFolder != null) {
            editTemplateDir.text = templateFolder.fsName;
        }
    };

    btnSelectImageDir.onClick = function () {
        imageFolder = Folder.selectDialog('请选择图片文件目录');
        if (imageFolder != null) {
            editImageDir.text = imageFolder.fsName;
        }
    };

    btnCancel.onClick = function () {
        mainWindow.close();
    };

    btnOk.onClick = function () {
        logToFile('脚本开始运行', csvFile.path + '/log.txt');
        if (editDataFile.text == '' && csvFile == undefined) {
            alert('请设置数据文件！');
            return;
        }
        processBatch();
        logToFile('脚本结束运行: ', csvFile.path + '/log.txt');
        mainWindow.close();
    };

    mainWindow.onClose = function () {
        saveSettings();
    };

    loadSettings();
    mainWindow.show();

    // --- 核心逻辑函数 ---

    function processBatch() {
        if (imageFolder == undefined && editImageDir.text != '不指定目录,默认表格内容为全路径' && editImageDir.text != '') {
            imageFolder = new Folder(editImageDir.text);
        }
        variableNames = editVariableSet.text.split(',');
        if (variableNames == undefined) {
            alert('变量设置有误!');
            return;
        }
        var outputFolder;
        if (savePathFolder == undefined && editSavePath.text == '不指定目录,默认导出到数据文件夹下') {
            outputFolder = new Folder(csvFile.path + '/导出');
        } else {
            outputFolder = new Folder(editSavePath.text);
        }
        if (!outputFolder.exists) {
            outputFolder.create();
        }

        if (app.documents.length > 0) {
            activeTemplateDoc = app.activeDocument;
            createSnapshot('模板');
            isTemplateFromActiveDoc = true;
            logToFile('以当前打开文档为模板开始处理', csvFile.path + '/log.txt');
        }

        for (var i = 0; i < csvDataRows.length; i++) {
            if (!isTemplateFromActiveDoc) {
                if (editTemplateDir.text == '') {
                    templateFileName = editTemplateSet.text;
                } else {
                    templateFileName = editTemplateDir.text + '/' + editTemplateSet.text;
                }
                for (var j = 0; j < csvHeaders.length; j++) {
                    var headerIndex = getHeaderIndex(csvHeaders[j]);
                    templateFileName = templateFileName.replace('%' + csvHeaders[j] + '%', csvDataRows[i][headerIndex]);
                }
                var templateFile = new File(templateFileName);
                if (!templateFile.exists) {
                    logToFile('指定模板' + templateFile.fsName + '不存在,跳过处理下一个', csvFile.path + '/error.txt');
                    continue;
                } else {
                    logToFile('打开模板' + templateFile.fsName, csvFile.path + '/log.txt');
                    activeTemplateDoc = app.open(templateFile);
                }
            }

            collectLayersToProcess();

            for (var k = 0; k < layersToProcess.length; k++) {
                var currentLayer = layersToProcess[k];
                var isVisible = currentLayer.visible;
                var layerName = currentLayer.name;
                var headerIndex = getHeaderIndex(layerName);

                if (currentLayer.kind == LayerKind.TEXT) {
                    logToFile('图层' + layerName + '替换文本:' + csvDataRows[i][headerIndex], csvFile.path + '/log.txt');
                    currentLayer.textItem.contents = csvDataRows[i][headerIndex];
                    currentLayer.name = layerName; // 确保名称不变
                } else {
                    var imageFile;
                    if (imageFolder != undefined) {
                        imageFile = new File(imageFolder + '/' + csvDataRows[i][headerIndex]);
                    } else {
                        imageFile = new File(csvDataRows[i][headerIndex]);
                    }

                    if (!imageFile.exists) {
                        logToFile('图层' + layerName + '替换图片:' + imageFile.fsName + '不存在', csvFile.path + '/log.txt');
                        continue;
                    } else {
                        logToFile('图层' + layerName + '替换图片:' + imageFile.fsName, csvFile.path + '/log.txt');
                    }

                    var scaleMode;
                    if (rbtnStretch.value) {
                        scaleMode = 1; // 拉伸
                    } else if (rbtnFitOverflow.value) {
                        scaleMode = 2; // 等比溢出
                    } else if (rbtnFitFill.value) {
                        scaleMode = 3; // 等比填充
                    } else if (rbtnNoScale.value) {
                        scaleMode = 4; // 不缩放
                    }

                    if (currentLayer.kind == LayerKind.SMARTOBJECT) {
                        activeTemplateDoc.activeLayer = currentLayer;
                        editSmartObjectContents();
                        var smartObjectDoc = app.activeDocument;
                        var isBackground = false;
                        if (smartObjectDoc.layers.length == 1) {
                            if (smartObjectDoc.layers[0].isBackgroundLayer) {
                                isBackground = true;
                            }
                        }
                        var originalLayer = smartObjectDoc.activeLayer;
                        var originalBounds = originalLayer.bounds;
                        placeImage(imageFile);
                        var newLayer = smartObjectDoc.activeLayer;
                        centerLayer(originalLayer, newLayer);
                        var smartObjectSize = [smartObjectDoc.width.value, smartObjectDoc.height.value];
                        resizeLayer(newLayer, smartObjectSize, scaleMode);
                        originalLayer.remove();
                        if (isBackground) {
                            smartObjectDoc.flatten();
                        }
                        smartObjectDoc.close(SaveOptions.SAVECHANGES);
                    } else {
                        var originalBounds = currentLayer.bounds;
                        placeImage(imageFile);
                        var newLayer = activeTemplateDoc.activeLayer;
                        centerLayer(currentLayer, newLayer);
                        var originalSize = [originalBounds[2].value - originalBounds[0].value, originalBounds[3].value - originalBounds[1].value];
                        resizeLayer(newLayer, originalSize, scaleMode);
                        newLayer.move(currentLayer, ElementPlacement.PLACEAFTER);
                        newLayer.rasterize(RasterizeType.ENTIRELAYER);
                        currentLayer.remove();
                        currentLayer = newLayer;
                    }
                }
                currentLayer.name = layerName;
                currentLayer.visible = isVisible;
            }

            var outputFileName = editFilenameSet.text;
            if (outputFileName == '') {
                outputFileName = '%' + csvHeaders[0] + '%_%name%_%id%';
            }
            for (var j = 0; j < csvHeaders.length; j++) {
                var headerIndex = getHeaderIndex(csvHeaders[j]);
                outputFileName = outputFileName.replace('%' + csvHeaders[j] + '%', csvDataRows[i][headerIndex]);
            }
            outputFileName = outputFileName.replace('%time%', getCurrentTime('_'));
            outputFileName = outputFileName.replace('%name%', activeTemplateDoc.name.substr(0, activeTemplateDoc.name.length - 4));
            outputFileName = outputFileName.replace('%date%', getCurrentDate());
            outputFileName = outputFileName.replace('%id%', i);
            outputFileName = outputFolder + '/' + outputFileName;

            var saveFile = new File(outputFileName);
            var webSaveOptions = new ExportOptionsSaveForWeb();
            webSaveOptions.optimized = true;

            if (ckPsd.value) {
                var psdSaveOptions = new PhotoshopSaveOptions();
                activeTemplateDoc.saveAs(saveFile, psdSaveOptions, true, Extension.LOWERCASE);
                logToFile('保存PSD' + saveFile.fsName, csvFile.path + '/log.txt');
            }
            if (ckTiff.value) {
                var tiffSaveOptions = new TiffSaveOptions();
                tiffSaveOptions.layers = false;
                tiffSaveOptions.layerCompression = LayerCompression.ZIP;
                // 修复：TIF导出要求默认为储存透明度
                tiffSaveOptions.transparency = true;
                activeTemplateDoc.saveAs(saveFile, tiffSaveOptions, true, Extension.LOWERCASE);
                logToFile('保存TIF' + saveFile.fsName, csvFile.path + '/log.txt');
            }
            if (ckPng.value) {
                // 修复：PNG导出不再被CMYK限制，即使文档是CMYK的情况下也能导出PNG
                var originalMode = activeTemplateDoc.mode;
                var needModeConversion = false;

                // 如果是CMYK模式，临时转换为RGB模式以支持PNG导出
                if (originalMode == DocumentMode.CMYK) {
                    needModeConversion = true;
                    activeTemplateDoc.changeMode(ChangeMode.RGB);
                    logToFile('CMYK文档临时转换为RGB模式以支持PNG导出', csvFile.path + '/log.txt');
                }

                if (ckWeb.value) {
                    webSaveOptions.format = SaveDocumentType.PNG;
                    webSaveOptions.PNG8 = false;
                    webSaveOptions.transparency = true;
                    activeTemplateDoc.exportDocument(saveFile, ExportType.SAVEFORWEB, webSaveOptions);
                } else {
                    var pngSaveOptions = new PNGSaveOptions();
                    activeTemplateDoc.saveAs(saveFile, pngSaveOptions, true, Extension.LOWERCASE);
                }

                // 如果进行了模式转换，转换回原始模式
                if (needModeConversion) {
                    activeTemplateDoc.changeMode(ChangeMode.CMYK);
                    logToFile('PNG导出完成，文档已转换回CMYK模式', csvFile.path + '/log.txt');
                }

                logToFile('保存PNG' + saveFile.fsName, csvFile.path + '/log.txt');
            }
            if (ckJpeg.value) {
                if (ckWeb.value) {
                    webSaveOptions.format = SaveDocumentType.JPEG;
                    webSaveOptions.quality = 80;
                    activeTemplateDoc.exportDocument(saveFile, ExportType.SAVEFORWEB, webSaveOptions);
                } else {
                    activeTemplateDoc.saveAs(saveFile, getJpegSaveOptions(), true, Extension.LOWERCASE);
                }
                logToFile('保存JPG' + saveFile.fsName, csvFile.path + '/log.txt');
            }
            if (ckPdf.value) {
                var pdfSaveOptions = new PDFSaveOptions();
                activeTemplateDoc.saveAs(saveFile, pdfSaveOptions, true, Extension.LOWERCASE);
                logToFile('保存PDF' + saveFile.fsName, csvFile.path + '/log.txt');
            }

            if (!isTemplateFromActiveDoc) {
                logToFile('关闭模板文件' + activeTemplateDoc.name, csvFile.path + '/log.txt');
                activeTemplateDoc.close(SaveOptions.DONOTSAVECHANGES);
                activeTemplateDoc = null;
            } else {
                revertToSnapshot('模板');
            }
            app.purge(PurgeTarget.ALLCACHES);
        }
        alert('处理完成！');
        if (activeTemplateDoc != null) {
            activeTemplateDoc.close(SaveOptions.DONOTSAVECHANGES);
        }
    }

    function centerLayer(targetLayer, layerToMove) {
        var targetBounds = targetLayer.bounds;
        var moveBounds = layerToMove.bounds;
        var targetWidth = targetBounds[2].value - targetBounds[0].value;
        var targetHeight = targetBounds[3].value - targetBounds[1].value;
        var moveWidth = moveBounds[2].value - moveBounds[0].value;
        var moveHeight = moveBounds[3].value - moveBounds[1].value;
        layerToMove.translate(targetBounds[0].value - moveBounds[0].value - (moveWidth - targetWidth) / 2, targetBounds[1].value - moveBounds[1].value - (moveHeight - targetHeight) / 2);
    }

    function collectLayersToProcess() {
        var allLayers = activeTemplateDoc.layers;
        scanLayers(allLayers);
        layersToProcess = [];
        while (layersToScan.length > 0) {
            var currentLayer = layersToScan.pop();
            if (currentLayer.typename == 'LayerSet') {
                scanLayers(currentLayer.layers);
            } else {
                for (var i = 0; i < variableNames.length; i++) {
                    if (currentLayer.name == variableNames[i]) {
                        layersToProcess.push(currentLayer);
                    }
                }
            }
        }
    }

    function getCurrentTime(separator) {
        var d = new Date();
        if (separator == undefined) {
            separator = ':';
        }
        return d.getHours() + separator + d.getMinutes() + separator + d.getSeconds();
    }

    function getCurrentDate(separator) {
        var d = new Date();
        if (separator == undefined) {
            separator = '-';
        }
        return d.getFullYear() + separator + (d.getMonth() + 1) + separator + d.getDate();
    }

    function scanLayers(layers) {
        for (var i = 0; i < layers.length; i++) {
            layersToScan.push(layers[i]);
        }
    }

    function getHeaderIndex(headerName) {
        for (var n = 0; n < csvHeaders.length; n++) {
            if (headerName == csvHeaders[n]) {
                return n;
            }
        }
        return -1;
    }

    function getJpegSaveOptions() {
        var opts = new JPEGSaveOptions();
        opts.embedProfile = true;
        opts.formatOptions = FormatOptions.STANDARDBASELINE;
        opts.quality = 12;
        return opts;
    }

    function logToFile(message, filePath) {
        message = getCurrentDate() + ' ' + getCurrentTime() + ' ' + message;
        var logFile = new File(filePath);
        logFile.open('a', 'TEXT', '????');
        logFile.write(message + '\n');
        logFile.close();
    }

    function saveSettings() {
        var presetXml = new XML('<set></set>');
        // 修复：确保数据文件路径也被保存
        presetXml.datafile = editDataFile.text;
        presetXml.datamoban = editTemplateDir.text;
        presetXml.dataimg = editImageDir.text;
        presetXml.editsavepath = editSavePath.text;
        presetXml.mobanset = editTemplateSet.text;
        presetXml.filenameset = editFilenameSet.text;
        presetXml.psd = ckPsd.value;
        presetXml.tif = ckTiff.value;
        presetXml.png = ckPng.value;
        presetXml.jpg = ckJpeg.value;
        presetXml.pdf = ckPdf.value;
        presetXml.web = ckWeb.value;
        presetXml.suofang = rbtnStretch.value;
        presetXml.dengbiyichu = rbtnFitOverflow.value;
        presetXml.dengbitianchong = rbtnFitFill.value;
        presetXml.rbtnno = rbtnNoScale.value;
        writeFile(settingsFilePath, presetXml.toXMLString());
    }

    function loadSettings() {
        var presetInfo = readFile(settingsFilePath);
        if (presetInfo != false) {
            var presetXml = new XML(presetInfo);
            ckPsd.value = (presetXml.psd == 'true');
            ckTiff.value = (presetXml.tif == 'true');
            ckPng.value = (presetXml.png == 'true');
            ckJpeg.value = (presetXml.jpg == 'true');
            ckPdf.value = (presetXml.pdf == 'true');
            ckWeb.value = (presetXml.web == 'true');
            rbtnStretch.value = (presetXml.suofang == 'true');
            rbtnFitOverflow.value = (presetXml.dengbiyichu == 'true');
            rbtnFitFill.value = (presetXml.dengbitianchong == 'true');
            rbtnNoScale.value = (presetXml.rbtnno == 'true');
            // 修复：确保数据文件路径也被加载
            if (presetXml.datafile != undefined && presetXml.datafile != '') {
                editDataFile.text = presetXml.datafile;
            }
            if (presetXml.datamoban != undefined && presetXml.datamoban != '') {
                editTemplateDir.text = presetXml.datamoban;
            }
            if (presetXml.dataimg != undefined && presetXml.dataimg != '') {
                editImageDir.text = presetXml.dataimg;
            }
            if (presetXml.editsavepath != undefined && presetXml.editsavepath != '') {
                editSavePath.text = presetXml.editsavepath;
            }
            if (presetXml.mobanset != undefined && presetXml.mobanset != '') {
                editTemplateSet.text = presetXml.mobanset;
            }
            if (presetXml.filenameset != undefined && presetXml.filenameset != '') {
                editFilenameSet.text = presetXml.filenameset;
            }
        }
    }

    function writeFile(filePath, content) {
        var file = new File(filePath);
        file.lineFeed = 'Windows';
        file.open('w', 'TEXT', '????');
        file.write(content);
        file.close();
    }

    function readFile(filePath) {
        var file = new File(filePath);
        if (!file.exists) {
            return false;
        }
        file.open('r');
        var content = file.read();
        file.close();
        return content;
    }

    function placeImage(filePath) {
        try {
            var desc = new ActionDescriptor();
            desc.putPath(stringIDToTypeID('null'), filePath);
            desc.putEnumerated(stringIDToTypeID('freeTransformCenterState'), stringIDToTypeID('quadCenterState'), stringIDToTypeID('QCSAverage'));
            var offsetDesc = new ActionDescriptor();
            offsetDesc.putUnitDouble(stringIDToTypeID('horizontal'), stringIDToTypeID('distanceUnit'), 0.0);
            offsetDesc.putUnitDouble(stringIDToTypeID('vertical'), stringIDToTypeID('distanceUnit'), 0.0);
            desc.putObject(stringIDToTypeID('offset'), stringIDToTypeID('offset'), offsetDesc);
            executeAction(stringIDToTypeID('placeEvent'), desc, DialogModes.NO);
        } catch (e) {
            if (e.number != 0x1f47) { // 8007 is "User cancelled"
                alert('Line: ' + e.line + '\n\n' + e, 'Bug!', true);
                throw e;
            }
        }
    }

    function editSmartObjectContents() {
        try {
            executeAction(stringIDToTypeID('placedLayerEditContents'), new ActionDescriptor(), DialogModes.NO);
        } catch (e) {
            if (e.number != 0x1f47) {
                alert('Line: ' + e.line + '\n\n' + e, 'Bug!', true);
                throw e;
            }
        }
    }

    function resizeLayer(layer, targetSize, mode, anchor) {
        if (anchor == undefined) {
            anchor = 5; // MiddleCenter
        }
        if (mode == undefined) {
            mode = 1; // Stretch
        }
        var anchorPosition;
        if (anchor == 1) { anchorPosition = AnchorPosition.BOTTOMLEFT; }
        else if (anchor == 2) { anchorPosition = AnchorPosition.BOTTOMCENTER; }
        else if (anchor == 3) { anchorPosition = AnchorPosition.BOTTOMRIGHT; }
        else if (anchor == 4) { anchorPosition = AnchorPosition.MIDDLELEFT; }
        else if (anchor == 5) { anchorPosition = AnchorPosition.MIDDLECENTER; }
        else if (anchor == 6) { anchorPosition = AnchorPosition.MIDDLERIGHT; }
        else if (anchor == 7) { anchorPosition = AnchorPosition.TOPLEFT; }
        else if (anchor == 8) { anchorPosition = AnchorPosition.TOPCENTER; }
        else if (anchor == 9) { anchorPosition = AnchorPosition.TOPRIGHT; }

        var targetWidth = targetSize[0];
        var targetHeight = targetSize[1];
        var bounds = layer.bounds;
        var width = bounds[2].value - bounds[0].value;
        var height = bounds[3].value - bounds[1].value;

        if (mode == 1) { // Stretch
            layer.resize(targetWidth / width * 100, targetHeight / height * 100, anchorPosition);
        } else if (mode == 2) { // Fit Overflow (Cover)
            if (width < height) {
                if (targetWidth / width * height < targetHeight) {
                    layer.resize(targetHeight / height * 100, targetHeight / height * 100, anchorPosition);
                } else {
                    layer.resize(targetWidth / width * 100, targetWidth / width * 100, anchorPosition);
                }
            } else {
                if (targetHeight / height * width < targetWidth) {
                    layer.resize(targetWidth / width * 100, targetWidth / width * 100, anchorPosition);
                } else {
                    layer.resize(targetHeight / height * 100, targetHeight / height * 100, anchorPosition);
                }
            }
        } else if (mode == 3) { // Fit Fill (Contain)
            if (width > height) {
                if (targetWidth / width * height > targetHeight) {
                    layer.resize(targetHeight / height * 100, targetHeight / height * 100, anchorPosition);
                } else {
                    layer.resize(targetWidth / width * 100, targetWidth / width * 100, anchorPosition);
                }
            } else {
                if (targetHeight / height * width > targetWidth) {
                    layer.resize(targetWidth / width * 100, targetWidth / width * 100, anchorPosition);
                } else {
                    layer.resize(targetHeight / height * 100, targetHeight / height * 100, anchorPosition);
                }
            }
        }
        // mode 4 (No Scale) does nothing
    }

    function createSnapshot(name) {
        var desc = new ActionDescriptor();
        var ref1 = new ActionReference();
        ref1.putClass(stringIDToTypeID('snapshotClass'));
        desc.putReference(stringIDToTypeID('null'), ref1);
        var ref2 = new ActionReference();
        ref2.putProperty(stringIDToTypeID('historyState'), stringIDToTypeID('currentHistoryState'));
        desc.putReference(stringIDToTypeID('from'), ref2);
        desc.putString(stringIDToTypeID('name'), name);
        desc.putEnumerated(stringIDToTypeID('using'), stringIDToTypeID('historyState'), stringIDToTypeID('fullDocument'));
        desc.putBoolean(stringIDToTypeID('replaceExisting'), true);
        executeAction(stringIDToTypeID('make'), desc);
    }

    function revertToSnapshot(name) {
        var desc = new ActionDescriptor();
        var ref = new ActionReference();
        ref.putName(stringIDToTypeID('snapshotClass'), name);
        desc.putReference(stringIDToTypeID('null'), ref);
        executeAction(stringIDToTypeID('select'), desc);
    }
}

// --- 试用和密码验证逻辑 ---
var enableTrial = true;
var trialUsesLimit = 0; // 0 for unlimited uses
var trialDaysLimit = 0; // 0 for unlimited days
var requirePassword = true;
var trialDataFile = new File(Folder.userData + '/');
if (!trialDataFile.exists) {
    writeTrialData(trialDataFile, '0;' + getInitialDateString());
};

if (enableTrial == true) {
    var trialData = readTrialData(trialDataFile).split(';');
    if (Number(trialData[0]) < 1) {
        runPasswordCheck();
    } else if (requirePassword) {
        runPasswordCheck();
    } else {
        if (isTrialValid()) {
            var trialData = readTrialData(trialDataFile).split(';');
            writeTrialData(trialDataFile, Number(trialData[0]) + 1 + ';' + trialData[1]);
            createMainWindow();
        } else {
            alert('体验已到期，请购买正版！');
        }
    }
} else if (requirePassword) {
    runPasswordCheck();
} else {
    if (isTrialValid()) {
        var trialData = readTrialData(trialDataFile).split(';');
        writeTrialData(trialDataFile, Number(trialData[0]) + 1 + ';' + trialData[1]);
        createMainWindow();
    } else {
        alert('体验已到期，请购买正版！');
    }
}

function runPasswordCheck() {
    var correctUser = '52cnp';
    var correctPass = 'maples';
    var result = showPasswordDialog(correctUser, correctPass);
    if (result == 1) {
        if (isTrialValid()) {
            var trialData = readTrialData(trialDataFile).split(';');
            writeTrialData(trialDataFile, Number(trialData[0]) + 1 + ';' + trialData[1]);
            createMainWindow();
        } else {
            alert('体验已到期，请购买正版！');
        }
    } else if (result == -1) {
        alert('密码错误无法运行！');
    }
}

function isTrialValid() {
    var trialData = readTrialData(trialDataFile).split(';');
    if (trialUsesLimit != '' && trialUsesLimit != 0) {
        if (Number(trialData[0]) > Number(trialUsesLimit)) {
            return false;
        }
    }
    if (trialDaysLimit != '' && trialDaysLimit != 0) {
        var dateParts = trialData[1].split('-');
        var startDate = new Date(Number(dateParts[0]), Number(dateParts[1]) - 1, Number(dateParts[2]), Number(dateParts[3]), Number(dateParts[4]));
        var now = new Date();
        var minutesPassed = (now - startDate) / 1000 / 60;
        if (minutesPassed > trialDaysLimit) {
            return false;
        }
    }
    return true;
}

function readTrialData(file) {
    var f = new File(file);
    if (!f.exists) {
        return '';
    } else {
        f.open('r');
        var content = f.read();
        f.close();
        return decodeData(content);
    }
}

function writeTrialData(file, data) {
    var f = new File(file);
    if (f.exists) {
        f.remove();
    }
    f.open('a');
    var encodedData = encodeData(data);
    var success = f.writeln(encodedData);
    f.close();
    f.hidden = true;
    return success;
}

function getInitialDateString(separator) {
    var d = new Date();
    if (separator == undefined) {
        separator = '-';
    }
    return d.getFullYear() + separator + (d.getMonth() + 1) + separator + d.getDate() + separator + d.getHours() + separator + d.getMinutes();
}

// "加密" 函数
function encodeData(inputString) {
    var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    var charsLength = chars.length;
    var charArray = chars.split('');
    var result = '', charCode, p1, p2, p3;
    for (var i = 0; i < inputString.length; i++) {
        charCode = inputString.charCodeAt(i);
        p1 = charCode % charsLength;
        charCode = (charCode - p1) / charsLength;
        p2 = charCode % charsLength;
        charCode = (charCode - p2) / charsLength;
        p3 = charCode % charsLength;
        result += charArray[p3] + charArray[p2] + charArray[p1];
    }
    return result;
}

// "解密" 函数
function decodeData(encodedString) {
    var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    var charsLength = chars.length;
    var p1, p2, p3, charCode, i = 0, resultArr;
    resultArr = new Array(Math.floor(encodedString.length / 3));
    var len = resultArr.length;
    for (var j = 0; j < len; j++) {
        p3 = chars.indexOf(encodedString.charAt(i));
        i++;
        p2 = chars.indexOf(encodedString.charAt(i));
        i++;
        p1 = chars.indexOf(encodedString.charAt(i));
        i++;
        resultArr[j] = p3 * charsLength * charsLength + p2 * charsLength + p1;
    }
    var resultString = eval('String.fromCharCode(' + resultArr.join(',') + ')');
    return resultString;
}

function showPasswordDialog(correctUser, correctPass) {
    var verificationFile = File(Folder.userData + "/verifiedStatus.txt");

    // 检查是否已经验证过
    if (verificationFile.exists) {
        verificationFile.open('r');
        var verifiedStatus = verificationFile.read();
        verificationFile.close();
        if (verifiedStatus == "verified") {
            return 1; // 已经验证过，直接返回成功
        }
    }

    var passDialog = new Window('dialog');
    passDialog.text = '密码验证';
    passDialog.orientation = 'column';
    passDialog.alignChildren = ['center', 'top'];
    passDialog.spacing = 10;
    passDialog.margins = 16;

    var userGroup = passDialog.add('group', undefined, { name: 'group1' });
    userGroup.orientation = 'row';
    userGroup.alignChildren = ['left', 'center'];
    userGroup.spacing = 10;
    userGroup.margins = 0;
    userGroup.add('statictext', undefined, '账号', { name: 'statictext1' });
    var editUser = userGroup.add('edittext {properties: {name: "txtid"}}');
    editUser.preferredSize.width = 150;

    var passGroup = passDialog.add('group', undefined, { name: 'group2' });
    passGroup.orientation = 'row';
    passGroup.alignChildren = ['left', 'center'];
    passGroup.spacing = 10;
    passGroup.margins = 0;
    passGroup.add('statictext', undefined, '密码', { name: 'statictext2' });
    var editPass = passGroup.add('edittext {properties: {name: "txtpas", noecho: true}}');
    editPass.preferredSize.width = 150;

    var btnGroup = passDialog.add('group', undefined, { name: 'group3' });
    btnGroup.orientation = 'row';
    btnGroup.alignChildren = ['left', 'center'];
    btnGroup.spacing = 10;
    btnGroup.margins = 0;
    var btnQuit = btnGroup.add('button', undefined, '取消', { name: 'btnquit' });
    var btnLogin = btnGroup.add('button', undefined, '确定', { name: 'btnok' });

    var dialogResult;
    btnQuit.onClick = function () {
        dialogResult = 0;
        passDialog.close();
    };

    btnLogin.onClick = function () {
        if (correctUser == editUser.text && correctPass == editPass.text) {
            dialogResult = 1;
            // 验证成功，保存状态为已验证
            verificationFile.open('w');
            verificationFile.write("verified");
            verificationFile.close();
        } else {
            dialogResult = -1;
        }
        passDialog.close();
    };

    passDialog.show();
    return dialogResult;
}
